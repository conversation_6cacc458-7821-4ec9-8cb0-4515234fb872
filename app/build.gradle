plugins {
    id 'com.android.application'
}

android {
    compileSdkVersion 31
    buildToolsVersion "30.0.3"

    defaultConfig {
        applicationId "xyz.gianlu.librespot.android"
        minSdkVersion 23
        targetSdkVersion 31
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    buildFeatures {
        viewBinding true
    }

    compileOptions {
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled true

        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    packagingOptions {
        exclude 'log4j2.xml'
        exclude 'META-INF/DEPENDENCIES'
    }
}

dependencies {
    implementation project(path: ':librespot-android-zeroconf-server')
    implementation 'androidx.preference:preference:1.1.+'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.1.5'

    implementation 'androidx.appcompat:appcompat:1.4.0'
    implementation 'com.google.android.material:material:1.4.0'

    implementation('xyz.gianlu.librespot:librespot-player:1.6.5:thin') {
        exclude group: 'xyz.gianlu.librespot', module: 'librespot-sink'
        exclude group: 'com.lmax', module: 'disruptor'
        exclude group: 'org.apache.logging.log4j'
        exclude group: 'commons-net', module: 'commons-net'
    }

    implementation project(':librespot-android-decoder')
    implementation project(':librespot-android-decoder-tremolo')
    implementation project(':librespot-android-sink')
    implementation 'uk.uuid.slf4j:slf4j-android:1.7.30-0'
}

configurations.all {
    resolutionStrategy {
        force 'org.slf4j:slf4j-api:1.7.30'
    }
}