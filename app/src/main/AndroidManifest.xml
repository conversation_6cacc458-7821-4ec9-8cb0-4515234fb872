<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="xyz.gianlu.librespot.android" >

    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:name=".LibrespotConnectApp"
        android:allowBackup="true"
        android:fullBackupContent="@xml/backup_descriptor"
        android:icon="@drawable/librespot_connect_logo"
        android:label="@string/app_name"
        android:roundIcon="@drawable/librespot_connect_logo"
        android:supportsRtl="true"
        android:theme="@style/Theme.LibrespotAndroid"
        android:usesCleartextTraffic="true" >
        <activity
            android:name=".SettingsActivity"
            android:exported="false"
            android:label="@string/titleActivitySettings" >
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name=".MainActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

</manifest>