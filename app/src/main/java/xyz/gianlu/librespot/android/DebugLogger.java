package xyz.gianlu.librespot.android;

import android.content.Context;
import android.os.Environment;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Debug logger that writes detailed logs to /sdcard/Documents/librespot_debug.txt
 * This helps track what's happening in the background and troubleshoot issues.
 */
public class DebugLogger {
    private static final String TAG = "DebugLogger";
    private static final String LOG_FILE_NAME = "librespot_debug.txt";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.US);
    
    private static DebugLogger instance;
    private static File logFile;
    private static boolean initialized = false;
    
    private DebugLogger() {}
    
    public static synchronized DebugLogger getInstance() {
        if (instance == null) {
            instance = new DebugLogger();
        }
        return instance;
    }
    
    /**
     * Initialize the debug logger with context
     */
    public static void initialize(@NonNull Context context) {
        if (initialized) return;
        
        try {
            // Try to use Documents folder first
            File documentsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
            if (!documentsDir.exists()) {
                documentsDir.mkdirs();
            }
            
            logFile = new File(documentsDir, LOG_FILE_NAME);
            
            // If Documents folder is not accessible, use app's external files dir
            if (!documentsDir.canWrite()) {
                File externalFilesDir = context.getExternalFilesDir(null);
                if (externalFilesDir != null) {
                    logFile = new File(externalFilesDir, LOG_FILE_NAME);
                }
            }
            
            // Clear previous log on app start
            if (logFile.exists()) {
                logFile.delete();
            }
            
            initialized = true;
            
            // Log initialization
            logInfo("DebugLogger", "=== LIBRESPOT DEBUG LOG STARTED ===");
            logInfo("DebugLogger", "Log file location: " + logFile.getAbsolutePath());
            logInfo("DebugLogger", "App version: 1.0 (Librespot 1.6.5)");
            logInfo("DebugLogger", "Timestamp: " + DATE_FORMAT.format(new Date()));
            logInfo("DebugLogger", "========================================");
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize debug logger", e);
        }
    }
    
    /**
     * Log info level message
     */
    public static void logInfo(@NonNull String tag, @NonNull String message) {
        log("INFO", tag, message, null);
    }
    
    /**
     * Log debug level message
     */
    public static void logDebug(@NonNull String tag, @NonNull String message) {
        log("DEBUG", tag, message, null);
    }
    
    /**
     * Log warning level message
     */
    public static void logWarning(@NonNull String tag, @NonNull String message) {
        log("WARN", tag, message, null);
    }
    
    /**
     * Log error level message
     */
    public static void logError(@NonNull String tag, @NonNull String message) {
        log("ERROR", tag, message, null);
    }
    
    /**
     * Log error level message with exception
     */
    public static void logError(@NonNull String tag, @NonNull String message, @Nullable Throwable throwable) {
        log("ERROR", tag, message, throwable);
    }
    
    /**
     * Log session events
     */
    public static void logSession(@NonNull String event, @NonNull String details) {
        log("SESSION", "LibrespotSession", event + ": " + details, null);
    }
    
    /**
     * Log player events
     */
    public static void logPlayer(@NonNull String event, @NonNull String details) {
        log("PLAYER", "LibrespotPlayer", event + ": " + details, null);
    }
    
    /**
     * Log zeroconf events
     */
    public static void logZeroconf(@NonNull String event, @NonNull String details) {
        log("ZEROCONF", "AndroidZeroconf", event + ": " + details, null);
    }
    
    /**
     * Log network events
     */
    public static void logNetwork(@NonNull String event, @NonNull String details) {
        log("NETWORK", "Network", event + ": " + details, null);
    }
    
    /**
     * Log audio events
     */
    public static void logAudio(@NonNull String event, @NonNull String details) {
        log("AUDIO", "AudioSink", event + ": " + details, null);
    }
    
    /**
     * Main logging method
     */
    private static synchronized void log(@NonNull String level, @NonNull String tag, @NonNull String message, @Nullable Throwable throwable) {
        if (!initialized || logFile == null) {
            // Fallback to Android log
            Log.println(Log.INFO, tag, message);
            if (throwable != null) {
                Log.e(tag, "Exception", throwable);
            }
            return;
        }
        
        try {
            String timestamp = DATE_FORMAT.format(new Date());
            String threadName = Thread.currentThread().getName();
            
            StringBuilder logEntry = new StringBuilder();
            logEntry.append(timestamp)
                    .append(" [").append(level).append("] ")
                    .append("[").append(threadName).append("] ")
                    .append(tag).append(": ")
                    .append(message);
            
            if (throwable != null) {
                logEntry.append("\n").append(getStackTrace(throwable));
            }
            
            logEntry.append("\n");
            
            // Write to file
            try (FileWriter writer = new FileWriter(logFile, true)) {
                writer.write(logEntry.toString());
                writer.flush();
            }
            
            // Also log to Android logcat
            int priority = getLogPriority(level);
            Log.println(priority, tag, message);
            if (throwable != null) {
                Log.e(tag, "Exception", throwable);
            }
            
        } catch (IOException e) {
            Log.e(TAG, "Failed to write to log file", e);
        }
    }
    
    /**
     * Get Android log priority from level string
     */
    private static int getLogPriority(@NonNull String level) {
        switch (level) {
            case "DEBUG": return Log.DEBUG;
            case "INFO": return Log.INFO;
            case "WARN": return Log.WARN;
            case "ERROR": return Log.ERROR;
            default: return Log.INFO;
        }
    }
    
    /**
     * Get stack trace as string
     */
    private static String getStackTrace(@NonNull Throwable throwable) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }
    
    /**
     * Get the log file path
     */
    @Nullable
    public static String getLogFilePath() {
        return logFile != null ? logFile.getAbsolutePath() : null;
    }
    
    /**
     * Check if logging is initialized
     */
    public static boolean isInitialized() {
        return initialized;
    }
}
