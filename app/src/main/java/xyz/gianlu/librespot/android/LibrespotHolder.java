package xyz.gianlu.librespot.android;

import androidx.annotation.Nullable;

import com.powerbling.librespot_android_zeroconf_server.AndroidZeroconfServer;

import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.lang.ref.WeakReference;

import xyz.gianlu.librespot.core.Session;
import xyz.gianlu.librespot.player.Player;

public final class LibrespotHolder {
    private static final String TAG = "LibrespotHolder";
    private volatile static WeakReference<Session> session;
    private volatile static WeakReference<Player> player;
    private volatile static WeakReference<AndroidZeroconfServer> server;

    private LibrespotHolder() {
    }

    public static void set(@NotNull Session session) {
        LibrespotHolder.session = new WeakReference<>(session);
        DebugLogger.logDebug(TAG, "Session stored in holder");
    }

    public static void set(@NotNull Player player) {
        LibrespotHolder.player = new WeakReference<>(player);
        DebugLogger.logDebug(TAG, "Player stored in holder");
    }

    public static void set(@NotNull AndroidZeroconfServer server) {
        LibrespotHolder.server = new WeakReference<>(server);
        DebugLogger.logDebug(TAG, "Server stored in holder");
    }

    public static void clear() {
        DebugLogger.logDebug(TAG, "Clearing all holders");

        Session s = getSession();
        Player p = getPlayer();
        AndroidZeroconfServer ser = getServer();

        if (p != null || s != null) {
            DebugLogger.logDebug(TAG, "Closing player and session in background thread");
            new Thread(() -> {
                if (p != null) {
                    DebugLogger.logDebug(TAG, "Closing player");
                    p.close();
                }

                try {
                    if (s != null) {
                        DebugLogger.logDebug(TAG, "Closing session");
                        s.close();
                    }
                } catch (IOException e) {
                    DebugLogger.logError(TAG, "Error closing session", e);
                }
            }).start();
        }

        if (ser != null) {
            try {
                DebugLogger.logDebug(TAG, "Closing server");
                ser.close();
            } catch (IOException e) {
                DebugLogger.logError(TAG, "Error closing server", e);
                e.printStackTrace();
            }
        }

        player = null;
        session = null;
        server = null;
        DebugLogger.logDebug(TAG, "All holders cleared");
    }

    @Nullable
    public static Session getSession() {
        return session != null ? session.get() : null;
    }

    @Nullable
    public static Player getPlayer() {
        return player != null ? player.get() : null;
    }

    @Nullable
    public static AndroidZeroconfServer getServer() {
        return server != null ? server.get() : null;
    }

    public static boolean hasSession() {
        return getSession() != null;
    }

    public static boolean hasPlayer() {
        return getPlayer() != null;
    }

    public static boolean hasServer() {
        return getServer() != null;
    }
}
