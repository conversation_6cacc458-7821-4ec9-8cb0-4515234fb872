package xyz.gianlu.librespot.android;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.graphics.BitmapFactory;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.widget.SeekBar;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.preference.PreferenceManager;

import com.powerbling.librespot_android_zeroconf_server.AndroidZeroconfServer;
import com.spotify.connectstate.Connect;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Range;

import java.io.IOException;
import java.util.Locale;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import xyz.gianlu.librespot.android.databinding.ActivityMainBinding;
import xyz.gianlu.librespot.android.sink.AndroidSinkOutput;
import xyz.gianlu.librespot.audio.MetadataWrapper;
import xyz.gianlu.librespot.core.Session;
import xyz.gianlu.librespot.metadata.PlayableId;
import xyz.gianlu.librespot.player.Player;
import xyz.gianlu.librespot.player.PlayerConfiguration;

public final class MainActivity extends AppCompatActivity {
    private static final String TAG = MainActivity.class.getSimpleName();
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private final Handler handler = new Handler(Looper.getMainLooper());

    @Override
    protected void onDestroy() {
        super.onDestroy();
        DebugLogger.logInfo(TAG, "MainActivity onDestroy - cleaning up resources");
        LibrespotHolder.clear();
        DebugLogger.logInfo(TAG, "MainActivity destroyed");
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.user_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        switch (item.getItemId()) {
            case R.id.settings_menu:
                LibrespotHolder.clear();
                startActivity(new Intent(this, SettingsActivity.class)
                        .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK));
                return true;
            case R.id.action_show_logs:
                showLogFileLocation();
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    private void showLogFileLocation() {
        String logPath = DebugLogger.getLogFilePath();
        if (logPath != null) {
            String message = "Debug logs are saved to:\n" + logPath +
                           "\n\nYou can find detailed information about what's happening in the background.";
            Toast.makeText(this, message, Toast.LENGTH_LONG).show();
            DebugLogger.logInfo(TAG, "Showed log file location to user: " + logPath);
        } else {
            Toast.makeText(this, "Debug logging not initialized", Toast.LENGTH_SHORT).show();
        }
    }

    private void startHeartbeatLogging() {
        // Log a heartbeat every 30 seconds to show the service is active
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!isDestroyed() && LibrespotHolder.hasServer()) {
                    DebugLogger.logZeroconf("HEARTBEAT", "Service is active and waiting for Spotify Connect clients");
                    handler.postDelayed(this, 30000); // 30 seconds
                }
            }
        }, 30000);
    }

    private void runNetworkDiagnostics() {
        executorService.execute(() -> {
            try {
                DebugLogger.logNetwork("DIAGNOSTICS_START", "Running network connectivity diagnostics");

                // Check WiFi connectivity
                ConnectivityManager cm = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
                if (cm != null) {
                    NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
                    if (activeNetwork != null && activeNetwork.isConnected()) {
                        DebugLogger.logNetwork("CONNECTIVITY", "Active network: " + activeNetwork.getTypeName() +
                                             ", Connected: " + activeNetwork.isConnected() +
                                             ", Available: " + activeNetwork.isAvailable());
                    } else {
                        DebugLogger.logNetwork("CONNECTIVITY", "No active network connection");
                    }
                }

                // Check if we can resolve some common hostnames (basic DNS test)
                try {
                    java.net.InetAddress.getByName("google.com");
                    DebugLogger.logNetwork("DNS_TEST", "DNS resolution working (google.com resolved)");
                } catch (Exception e) {
                    DebugLogger.logNetwork("DNS_TEST", "DNS resolution failed: " + e.getMessage());
                }

                // Check multicast support (required for mDNS)
                try {
                    java.net.MulticastSocket multicastSocket = new java.net.MulticastSocket();
                    multicastSocket.close();
                    DebugLogger.logNetwork("MULTICAST_TEST", "Multicast socket creation successful");
                } catch (Exception e) {
                    DebugLogger.logNetwork("MULTICAST_TEST", "Multicast socket creation failed: " + e.getMessage());
                }

                DebugLogger.logNetwork("DIAGNOSTICS_COMPLETE", "Network diagnostics completed");

            } catch (Exception e) {
                DebugLogger.logError(TAG, "Network diagnostics failed", e);
            }
        });
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        DebugLogger.logInfo(TAG, "MainActivity onCreate started");

        ActivityMainBinding binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        SharedPreferences pref = PreferenceManager.getDefaultSharedPreferences(this);
        String deviceName = pref.getString("preference_speaker_name", getString(R.string.app_name));
        binding.visibleAs.setText(deviceName);

        DebugLogger.logInfo(TAG, "Device name set to: " + deviceName);
        DebugLogger.logDebug(TAG, "UI binding completed");

        binding.playPauseButton.setOnClickListener((v) -> executorService.execute(new PlayPauseRunnable(() -> {})));

        binding.prev.setOnClickListener((v) ->
                executorService.execute(new PrevRunnable(() ->
                        Toast.makeText(this, R.string.skippedPrev, Toast.LENGTH_SHORT).show())));

        binding.next.setOnClickListener((v) ->
                executorService.execute(new NextRunnable(() ->
                        Toast.makeText(this, R.string.skippedNext, Toast.LENGTH_SHORT).show())));

        // Make seekbar immutable from user side
        binding.seekBar.setOnTouchListener(new SeekBar.OnTouchListener(){
            @SuppressLint("ClickableViewAccessibility")
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return true;
            }
        });

        Runnable resumeRunnable = () -> {
            binding.playPauseButton.setEnabled(true);
            binding.next.setEnabled(true);
            binding.prev.setEnabled(true);
            binding.playPauseButton.setImageResource(android.R.drawable.ic_media_pause);
        };

        Runnable pauseRunnable = () -> binding.playPauseButton.setImageResource(android.R.drawable.ic_media_play);

        Runnable closingRunnable = () -> {
            executorService.execute(new SessionClosingRunnable(() -> {}));

            binding.playPauseButton.setEnabled(false);
            binding.next.setEnabled(false);
            binding.prev.setEnabled(false);
            binding.songTitle.setText(R.string.noSong);
            binding.songArtist.setText(R.string.artist);
            binding.username.setText("");
            binding.connectedToLabel.setText(R.string.notConnected);
            binding.seekBar.setProgress(0);
            binding.songDuration.setText(R.string.invalidTime);
            binding.currentTime.setText(R.string.invalidTime);
            binding.songImage.setImageResource(R.drawable.defaultalbum);
        };




    Player.EventsListener playerListener = new Player.EventsListener() {
            private Runnable inactivityRunnable;
            private TimerTask timerTask;
            private final Handler handler = new Handler();
            private final Timer timer = new Timer();

            @Override
            public void onContextChanged(@NotNull Player player, @NotNull String s) {
                Log.i(TAG, "Context changed");
                DebugLogger.logPlayer("CONTEXT_CHANGED", "New context: " + s);
            }

            @Override
            public void onTrackChanged(@NotNull Player player, @NotNull PlayableId playableId, @org.jetbrains.annotations.Nullable MetadataWrapper metadataWrapper, boolean b) {
                String trackInfo = playableId != null ? playableId.toString() : "null";
                String metaInfo = metadataWrapper != null ? metadataWrapper.getName() + " by " + metadataWrapper.getArtist() : "no metadata";
                DebugLogger.logPlayer("TRACK_CHANGED", "Track: " + trackInfo + ", Metadata: " + metaInfo + ", Flag: " + b);
            }

            @Override
            public void onPlaybackEnded(@NotNull Player player) {
                Log.i(TAG, "Playback ended");
                DebugLogger.logPlayer("PLAYBACK_ENDED", "Playback has ended");
            }

            @Override
            public void onPlaybackPaused(@NotNull Player player, long l) {
                DebugLogger.logPlayer("PLAYBACK_PAUSED", "Playback paused at position: " + l + "ms");
                runOnUiThread(pauseRunnable);
            }

            @Override
            public void onPlaybackResumed(@NotNull Player player, long l) {
                DebugLogger.logPlayer("PLAYBACK_RESUMED", "Playback resumed at position: " + l + "ms");

                if (inactivityRunnable != null) {
                    handler.removeCallbacks(inactivityRunnable);
                    inactivityRunnable = null;
                    DebugLogger.logDebug(TAG, "Cancelled inactivity runnable");
                }

                runOnUiThread(resumeRunnable);

                timerTask =
                        new TimerTask() {
                            @Override
                            public void run() {
                                runOnUiThread(
                                        () -> {
                                            binding.seekBar.setProgress(player.time());
                                            binding.currentTime.setText(Utils.formatTimeString(player.time()));
                                        });
                            }};
                timer.scheduleAtFixedRate(timerTask, 0, 500);


            }

            @Override
            public void onTrackSeeked(@NotNull Player player, long l) {

            }


            @Override
            public void onMetadataAvailable(@NotNull Player player, @NotNull MetadataWrapper metadataWrapper) {
                runOnUiThread(() -> {

                    if (metadataWrapper != null) {
                        binding.songTitle.setText(metadataWrapper.getName());
                        binding.songArtist.setText(metadataWrapper.getArtist());

                        binding.seekBar.setMax(metadataWrapper.duration());
                        binding.songDuration.setText(Utils.formatTimeString(metadataWrapper.duration()));


                    } else {
                        binding.songTitle.setText(R.string.noSong);
                        binding.songArtist.setText(R.string.artist);
                    }



                });

                new AsyncTask<Void, Void, Void>() {
                    @Override
                    protected Void doInBackground(Void... voids) {
                        try {
                            byte[] imageData = player.currentCoverImage();

                            runOnUiThread(() -> binding.songImage.setImageBitmap(BitmapFactory
                                    .decodeByteArray(imageData, 0, imageData.length)));
                        } catch (IOException e) {
                            e.printStackTrace();
                        }

                        return null;
                    }
                }.execute();
            }

            @Override
            public void onPlaybackHaltStateChanged(@NotNull Player player, boolean b, long l) {
                Log.i(TAG, "Playback halt state changed");

            }

            @Override
            public void onInactiveSession(@NotNull Player player, boolean b) {
                Log.i(TAG, "Inactive session");

                timerTask.cancel();

                inactivityRunnable = closingRunnable;

                // Wait one minute before closing session
                handler.postDelayed(inactivityRunnable, 20000);
            }

            @Override
            public void onVolumeChanged(@NotNull Player player, @Range(from = 0L, to = 1L) float v) {

            }

            @Override
            public void onPanicState(@NotNull Player player) {

            }

            @Override
            public void onStartedLoading(@NotNull Player player) {
                Log.i(TAG, "Started loading");

            }

            @Override
            public void onFinishedLoading(@NotNull Player player) {
                Log.i(TAG, "Finished loading");


            }

            @Override
            public void onPlaybackFailed(@NotNull Player player, @NotNull Exception exception) {
                Log.e(TAG, "Playback failed", exception);
                DebugLogger.logError(TAG, "Playback failed: " + exception.getMessage(), exception);

                // Handle playback failure - could show error message to user
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, "Playback failed: " + exception.getMessage(), Toast.LENGTH_LONG).show();
                });
            }
        };

        AndroidZeroconfServer.SessionListener sessionListener = new AndroidZeroconfServer.SessionListener() {
            @Override
            public void sessionClosing(@NotNull Session session) {
                executorService.execute(new SessionClosingRunnable(() -> {
                    Toast.makeText(MainActivity.this, R.string.sessionClosing, Toast.LENGTH_SHORT).show();
                }));
                executorService.execute(closingRunnable);

            }

            @Override
            public void sessionChanged(@NotNull Session session) {
                executorService.execute(new SessionChangedRunnable(session, new SessionChangedCallback() {

                    @Override
                    public void playerReady(@NotNull Player player, @NotNull String username) {
                        Toast.makeText(MainActivity.this, R.string.playerReady, Toast.LENGTH_SHORT).show();
                        binding.connectedToLabel.setText(R.string.connectedTo);
                        binding.username.setText(username);
                        binding.playPauseButton.setEnabled(true);
                        binding.next.setEnabled(true);
                        binding.prev.setEnabled(true);

                        player.addEventsListener(playerListener);
                    }

                    @Override
                    public void failedGettingReady(@NotNull Exception ex) {
                        Toast.makeText(MainActivity.this, R.string.somethingWentWrong, Toast.LENGTH_SHORT).show();
                    }
                }));

            }
        };


        DebugLogger.logInfo(TAG, "Starting Librespot setup...");

        // Run network diagnostics
        runNetworkDiagnostics();

        executorService.execute(new SetupRunnable(sessionListener));

        DebugLogger.logDebug(TAG, "MainActivity onCreate completed");
    }


    private interface SimpleCallback {
        void done();
    }

    private interface SessionChangedCallback {
        void playerReady(@NotNull Player player, @NotNull String username);

        void failedGettingReady(@NotNull Exception ex);
    }

    private static class SessionClosingRunnable implements Runnable {
        private final SimpleCallback callback;
        private final Handler handler = new Handler(Looper.getMainLooper());

        SessionClosingRunnable(@NotNull SimpleCallback callback) {
            this.callback = callback;
        }

        @Override
        public void run() {
            LibrespotHolder.clear();

            handler.post(callback::done);
        }
    }

    private static class SessionChangedRunnable implements Runnable {
        private final SessionChangedCallback callback;
        private final Session session;
        private final Handler handler = new Handler(Looper.getMainLooper());

        SessionChangedRunnable(@NotNull Session session, @NotNull SessionChangedCallback callback) {
            this.callback = callback;
            this.session = session;
        }


        @Override
        public void run() {
            Log.i(TAG, "Connected to: " + session.username());
            DebugLogger.logSession("USER_CONNECTED", "Username: " + session.username());

            if (LibrespotHolder.hasSession()) {
                DebugLogger.logDebug(TAG, "Clearing existing session");
                LibrespotHolder.clear();
            }

            LibrespotHolder.set(session);
            DebugLogger.logSession("SESSION_SET", "Session stored in holder");

            DebugLogger.logPlayer("PLAYER_INIT", "Creating player with custom Android sink output");

            Player player;
            PlayerConfiguration configuration = new PlayerConfiguration.Builder()
                    .setOutput(PlayerConfiguration.AudioOutput.CUSTOM)
                    .setOutputClass(AndroidSinkOutput.class.getName())
                    .build();

            player = new Player(configuration, session);
            LibrespotHolder.set(player);
            DebugLogger.logPlayer("PLAYER_CREATED", "Player created and stored in holder");

            DebugLogger.logPlayer("PLAYER_WAIT", "Waiting for player to be ready (SDK: " + Build.VERSION.SDK_INT + ")");

            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.M) {
                DebugLogger.logDebug(TAG, "Using legacy wait method for older Android version");
                while (!player.isReady()) {
                    try {
                        //noinspection BusyWait
                        Thread.sleep(100);
                    } catch (InterruptedException ex) {
                        DebugLogger.logError(TAG, "Player wait interrupted", ex);
                        return;
                    }
                }
            } else {
                try {
                    player.waitReady();
                } catch (InterruptedException ex) {
                    DebugLogger.logError(TAG, "Player waitReady interrupted", ex);
                    LibrespotHolder.clear();
                    return;
                }
            }

            DebugLogger.logPlayer("PLAYER_READY", "Player is now ready for playback");

            handler.post(() -> callback.playerReady(player, session.username()));
        }
    }

    private class SetupRunnable implements Runnable {
        private final AndroidZeroconfServer.SessionListener sessionListener;

        SetupRunnable(@NotNull AndroidZeroconfServer.SessionListener sessionListener) {
            this.sessionListener = sessionListener;
        }

        @Override
        public void run() {

            try {
                DebugLogger.logZeroconf("SETUP_START", "Starting Zeroconf server setup");

                Session.Configuration conf = new Session.Configuration.Builder()
                        .setStoreCredentials(false)
                        .setCacheEnabled(false)
                        .build();

                DebugLogger.logSession("CONFIG_CREATED", "Session config: credentials=false, cache=false");

                SharedPreferences pref = PreferenceManager.getDefaultSharedPreferences(getBaseContext());
                String deviceName = pref.getString("preference_speaker_name", getString(R.string.app_name));
                String locale = Locale.getDefault().getLanguage();

                DebugLogger.logZeroconf("DEVICE_CONFIG", "Name: " + deviceName + ", Locale: " + locale + ", Type: SPEAKER");

                AndroidZeroconfServer.Builder builder = new AndroidZeroconfServer.Builder(getBaseContext(), conf)
                        .setPreferredLocale(locale)
                        .setDeviceType(Connect.DeviceType.SPEAKER)
                        .setDeviceId(null)
                        .setDeviceName(deviceName);

                AndroidZeroconfServer server = builder.create();
                DebugLogger.logZeroconf("SERVER_CREATED", "Zeroconf server created successfully");

                server.addSessionListener(sessionListener);
                DebugLogger.logZeroconf("LISTENER_ADDED", "Session listener added to server");

                LibrespotHolder.set(server);
                DebugLogger.logZeroconf("SERVER_STORED", "Server stored in holder");

                Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                    DebugLogger.logZeroconf("SHUTDOWN_HOOK", "Shutdown hook triggered, closing server");
                    try {
                        server.closeSession();
                        server.close();
                        DebugLogger.logZeroconf("SHUTDOWN_COMPLETE", "Server closed successfully");
                    } catch (IOException e) {
                        DebugLogger.logError(TAG, "Error during server shutdown", e);
                        e.printStackTrace();
                    }
                }));

                DebugLogger.logZeroconf("SETUP_COMPLETE", "Zeroconf server setup completed successfully");

                // Start periodic heartbeat logging to show the service is active
                startHeartbeatLogging();

            } catch (IOException e) {
                DebugLogger.logError(TAG, "Failed to setup Zeroconf server", e);
                e.printStackTrace();
            }


        }
    }

    private static class SeekRunnable implements Runnable {
        private final SimpleCallback callback;
        private final int pos;
        private final Handler handler = new Handler(Looper.getMainLooper());

        SeekRunnable(int pos, @NotNull SimpleCallback callback) {
            this.callback = callback;
            this.pos = pos;
        }

        @Override
        public void run() {
            Player player = LibrespotHolder.getPlayer();
            if (player == null) return;

            player.seek(pos);
            handler.post(callback::done);
        }
    }

    private static class PlayPauseRunnable implements Runnable {
        private final SimpleCallback callback;
        private final Handler handler = new Handler(Looper.getMainLooper());

        PlayPauseRunnable(@NotNull SimpleCallback callback) {
            this.callback = callback;
        }

        @Override
        public void run() {
            Player player = LibrespotHolder.getPlayer();
            if (player == null) return;

            player.playPause();
            handler.post(callback::done);
        }
    }

    private static class PrevRunnable implements Runnable {
        private final SimpleCallback callback;
        private final Handler handler = new Handler(Looper.getMainLooper());

        PrevRunnable(@NotNull SimpleCallback callback) {
            this.callback = callback;
        }

        @Override
        public void run() {
            Player player = LibrespotHolder.getPlayer();
            if (player == null) return;

            player.previous();
            handler.post(callback::done);
        }
    }

    private static class NextRunnable implements Runnable {
        private final SimpleCallback callback;
        private final Handler handler = new Handler(Looper.getMainLooper());

        NextRunnable(@NotNull SimpleCallback callback) {
            this.callback = callback;
        }

        @Override
        public void run() {
            Player player = LibrespotHolder.getPlayer();
            if (player == null) return;

            player.next();
            handler.post(callback::done);
        }
    }
}
