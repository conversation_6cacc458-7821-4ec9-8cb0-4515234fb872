<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"


    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Space
            android:layout_width="50dp"
            android:layout_height="match_parent" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/connectedToLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/notConnected" />

                <Space
                    android:layout_width="5dp"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/username"
                    android:layout_width="219dp"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/visible_as_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/visible_as" />

                <Space
                    android:layout_width="5dp"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/visible_as"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/app_name"
                    android:textColor="?android:attr/colorPressedHighlight"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <Space
        android:layout_width="match_parent"
        android:layout_height="51dp" />

    <LinearLayout
        android:id="@+id/songDetails"
        android:layout_width="match_parent"
        android:layout_height="452dp"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/songImage"
            android:layout_width="match_parent"
            android:layout_height="324dp"
            android:contentDescription="@string/noSong"
            app:srcCompat="@drawable/defaultalbum" />

        <Space
            android:layout_width="match_parent"
            android:layout_height="18dp" />

        <TextView
            android:id="@+id/songTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/noSong"
            android:textAlignment="center"
            android:textAppearance="@style/TextAppearance.AppCompat.Large"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/songArtist"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/artist"
            android:textAlignment="center"
            android:textAppearance="@style/TextAppearance.AppCompat.Body2" />

        <Space
            android:layout_width="match_parent"
            android:layout_height="17dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="32dp"
            android:orientation="horizontal">

            <Space
                android:layout_width="40dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/currentTime"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="@string/invalidTime"
                android:textAlignment="center" />

            <SeekBar
                android:id="@+id/seekBar"
                android:layout_width="332dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:clickable="false"
                android:indeterminate="false"
                android:longClickable="false"
                android:progressTint="?android:attr/colorPressedHighlight"
                android:theme="@style/Theme.LibrespotAndroid"
                android:thumbTint="?attr/colorPrimaryVariant" />

            <TextView
                android:id="@+id/songDuration"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="@string/invalidTime"
                android:textAlignment="textStart" />

            <Space
                android:layout_width="40dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/playControls"
        android:layout_width="match_parent"
        android:layout_height="51dp"
        android:layout_marginTop="24dp"
        android:gravity="center_vertical">

        <Space
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <ImageButton
            android:id="@+id/prev"
            style="@style/Widget.AppCompat.ImageButton"
            android:layout_width="50dp"
            android:layout_height="match_parent"
            android:background="#00FFFFFF"
            android:contentDescription="@string/previous"
            android:src="@android:drawable/ic_media_previous" />

        <Space
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <ImageButton
            android:id="@+id/playPauseButton"
            android:layout_width="50dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:background="#00FFFFFF"
            android:contentDescription="@string/play"
            android:src="@android:drawable/ic_media_play" />

        <Space
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <ImageButton
            android:id="@+id/next"
            android:layout_width="50dp"
            android:layout_height="match_parent"
            android:background="#00FFFFFF"
            android:contentDescription="@string/next"
            android:src="@android:drawable/ic_media_next" />

        <Space
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

    </LinearLayout>


</LinearLayout>