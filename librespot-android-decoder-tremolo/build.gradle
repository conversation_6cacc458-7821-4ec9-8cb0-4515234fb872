plugins {
    id 'com.android.library'
    id 'maven-publish'
}

android {
    compileSdkVersion 31
    buildToolsVersion "30.0.3"

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 31
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"

        // TODO use modern way to build native library
        sourceSets.main {
            jniLibs.srcDir 'src/main/libs'
            jni.srcDirs = [] //disable automatic ndk-build call
        }

        // call regular ndk-build(.cmd) script from app directory
        task ndkBuild(type: Exec) {
            commandLine 'ndk-build.cmd', '-C', file('src/main').absolutePath
        }

        tasks.withType(JavaCompile) {
            // compileTask -> compileTask.dependsOn ndkBuild
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'xyz.gianlu.librespot:librespot-decoder-api:1.6.2'
}