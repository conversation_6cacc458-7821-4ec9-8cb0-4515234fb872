/************************************************************************
 * Copyright (C) 2002-2009, Xiph.org Foundation
 * Copyright (C) 2010, <PERSON> for Pinknoise Productions Ltd
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 *     * Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above
 * copyright notice, this list of conditions and the following disclaimer
 * in the documentation and/or other materials provided with the
 * distribution.
 *     * Neither the names of the Xiph.org Foundation nor Pinknoise
 * Productions Ltd nor the names of its contributors may be used to
 * endorse or promote products derived from this software without
 * specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 ************************************************************************

 function: window lookup tables

 ************************************************************************/


#include "os_types.h"

static LOOKUP_T vwin64[32] = {
  X(0x001f0003), X(0x01168c98), X(0x030333c8), X(0x05dfe3a4),
  X(0x09a49562), X(0x0e45df18), X(0x13b47ef2), X(0x19dcf676),
  X(0x20a74d83), X(0x27f7137c), X(0x2fabb05a), X(0x37a1105a),
  X(0x3fb0ab28), X(0x47b2dcd1), X(0x4f807bc6), X(0x56f48e70),
  X(0x5dedfc79), X(0x64511653), X(0x6a08cfff), X(0x6f079328),
  X(0x734796f4), X(0x76cab7f2), X(0x7999d6e8), X(0x7bc3cf9f),
  X(0x7d5c20c1), X(0x7e7961df), X(0x7f33a567), X(0x7fa2e1d0),
  X(0x7fdd78a5), X(0x7ff6ec6d), X(0x7ffed0e9), X(0x7ffffc3f),
};

static LOOKUP_T vwin128[64] = {
  X(0x0007c04d), X(0x0045bb89), X(0x00c18b87), X(0x017ae294),
  X(0x02714a4e), X(0x03a4217a), X(0x05129952), X(0x06bbb24f),
  X(0x089e38a1), X(0x0ab8c073), X(0x0d09a228), X(0x0f8ef6bd),
  X(0x12469488), X(0x152e0c7a), X(0x1842a81c), X(0x1b81686d),
  X(0x1ee705d9), X(0x226ff15d), X(0x26185705), X(0x29dc21cc),
  X(0x2db700fe), X(0x31a46f08), X(0x359fb9c1), X(0x39a40c0c),
  X(0x3dac78b6), X(0x41b40674), X(0x45b5bcb0), X(0x49acb109),
  X(0x4d94152b), X(0x516744bd), X(0x5521d320), X(0x58bf98a5),
  X(0x5c3cbef4), X(0x5f95cc5d), X(0x62c7add7), X(0x65cfbf64),
  X(0x68abd2ba), X(0x6b5a3405), X(0x6dd9acab), X(0x7029840d),
  X(0x72497e38), X(0x7439d8ac), X(0x75fb4532), X(0x778ee30a),
  X(0x78f6367e), X(0x7a331f1a), X(0x7b47cccd), X(0x7c36b416),
  X(0x7d028192), X(0x7dae0d18), X(0x7e3c4caa), X(0x7eb04763),
  X(0x7f0d08a7), X(0x7f5593b7), X(0x7f8cd7d5), X(0x7fb5a513),
  X(0x7fd2a1fc), X(0x7fe64212), X(0x7ff2bd4c), X(0x7ffa0890),
  X(0x7ffdcf39), X(0x7fff6dac), X(0x7fffed01), X(0x7fffffc4),
};

static LOOKUP_T vwin256[128] = {
  X(0x0001f018), X(0x00117066), X(0x00306e9e), X(0x005ee5f1),
  X(0x009ccf26), X(0x00ea208b), X(0x0146cdea), X(0x01b2c87f),
  X(0x022dfedf), X(0x02b85ced), X(0x0351cbbd), X(0x03fa317f),
  X(0x04b17167), X(0x05776b90), X(0x064bfcdc), X(0x072efedd),
  X(0x082047b4), X(0x091fa9f1), X(0x0a2cf477), X(0x0b47f25d),
  X(0x0c706ad2), X(0x0da620ff), X(0x0ee8d3ef), X(0x10383e75),
  X(0x11941716), X(0x12fc0ff6), X(0x146fd6c8), X(0x15ef14c2),
  X(0x17796e8e), X(0x190e844f), X(0x1aadf196), X(0x1c574d6e),
  X(0x1e0a2a62), X(0x1fc61688), X(0x218a9b9c), X(0x23573f12),
  X(0x252b823d), X(0x2706e269), X(0x28e8d913), X(0x2ad0dc0e),
  X(0x2cbe5dc1), X(0x2eb0cd60), X(0x30a79733), X(0x32a224d5),
  X(0x349fdd8b), X(0x36a02690), X(0x38a2636f), X(0x3aa5f65e),
  X(0x3caa409e), X(0x3eaea2df), X(0x40b27da6), X(0x42b531b8),
  X(0x44b62086), X(0x46b4ac99), X(0x48b03a05), X(0x4aa82ed5),
  X(0x4c9bf37d), X(0x4e8af349), X(0x50749ccb), X(0x52586246),
  X(0x5435ba1c), X(0x560c1f31), X(0x57db1152), X(0x59a21591),
  X(0x5b60b6a3), X(0x5d168535), X(0x5ec31839), X(0x60660d36),
  X(0x61ff0886), X(0x638db595), X(0x6511c717), X(0x668af734),
  X(0x67f907b0), X(0x695bc207), X(0x6ab2f787), X(0x6bfe815a),
  X(0x6d3e4090), X(0x6e721e16), X(0x6f9a0ab5), X(0x70b5fef8),
  X(0x71c5fb16), X(0x72ca06cd), X(0x73c2313d), X(0x74ae90b2),
  X(0x758f4275), X(0x76646a85), X(0x772e335c), X(0x77eccda0),
  X(0x78a06fd7), X(0x79495613), X(0x79e7c19c), X(0x7a7bf894),
  X(0x7b064596), X(0x7b86f757), X(0x7bfe6044), X(0x7c6cd615),
  X(0x7cd2b16e), X(0x7d304d71), X(0x7d860756), X(0x7dd43e06),
  X(0x7e1b51ad), X(0x7e5ba355), X(0x7e95947e), X(0x7ec986bb),
  X(0x7ef7db4a), X(0x7f20f2b9), X(0x7f452c7f), X(0x7f64e6a7),
  X(0x7f807d71), X(0x7f984aff), X(0x7faca700), X(0x7fbde662),
  X(0x7fcc5b04), X(0x7fd85372), X(0x7fe21a99), X(0x7fe9f791),
  X(0x7ff02d58), X(0x7ff4fa9e), X(0x7ff89990), X(0x7ffb3faa),
  X(0x7ffd1d8b), X(0x7ffe5ecc), X(0x7fff29e0), X(0x7fff9ff3),
  X(0x7fffdcd2), X(0x7ffff6d6), X(0x7ffffed0), X(0x7ffffffc),
};

static LOOKUP_T vwin512[256] = {
  X(0x00007c06), X(0x00045c32), X(0x000c1c62), X(0x0017bc4c),
  X(0x00273b7a), X(0x003a9955), X(0x0051d51c), X(0x006cede7),
  X(0x008be2a9), X(0x00aeb22a), X(0x00d55b0d), X(0x00ffdbcc),
  X(0x012e32b6), X(0x01605df5), X(0x01965b85), X(0x01d02939),
  X(0x020dc4ba), X(0x024f2b83), X(0x02945ae6), X(0x02dd5004),
  X(0x032a07d3), X(0x037a7f19), X(0x03ceb26e), X(0x04269e37),
  X(0x04823eab), X(0x04e18fcc), X(0x05448d6d), X(0x05ab3329),
  X(0x06157c68), X(0x0683645e), X(0x06f4e607), X(0x0769fc25),
  X(0x07e2a146), X(0x085ecfbc), X(0x08de819f), X(0x0961b0cc),
  X(0x09e856e3), X(0x0a726d46), X(0x0affed1d), X(0x0b90cf4c),
  X(0x0c250c79), X(0x0cbc9d0b), X(0x0d577926), X(0x0df598aa),
  X(0x0e96f337), X(0x0f3b8026), X(0x0fe3368f), X(0x108e0d42),
  X(0x113bfaca), X(0x11ecf56b), X(0x12a0f324), X(0x1357e9ac),
  X(0x1411ce70), X(0x14ce9698), X(0x158e3702), X(0x1650a444),
  X(0x1715d2aa), X(0x17ddb638), X(0x18a842aa), X(0x19756b72),
  X(0x1a4523b9), X(0x1b175e62), X(0x1bec0e04), X(0x1cc324f0),
  X(0x1d9c9532), X(0x1e78508a), X(0x1f564876), X(0x20366e2e),
  X(0x2118b2a2), X(0x21fd0681), X(0x22e35a37), X(0x23cb9dee),
  X(0x24b5c18e), X(0x25a1b4c0), X(0x268f66f1), X(0x277ec74e),
  X(0x286fc4cc), X(0x29624e23), X(0x2a5651d7), X(0x2b4bbe34),
  X(0x2c428150), X(0x2d3a8913), X(0x2e33c332), X(0x2f2e1d35),
  X(0x30298478), X(0x3125e62d), X(0x32232f61), X(0x33214cfc),
  X(0x34202bc2), X(0x351fb85a), X(0x361fdf4f), X(0x37208d10),
  X(0x3821adf7), X(0x39232e49), X(0x3a24fa3c), X(0x3b26fdf6),
  X(0x3c292593), X(0x3d2b5d29), X(0x3e2d90c8), X(0x3f2fac7f),
  X(0x40319c5f), X(0x41334c81), X(0x4234a905), X(0x43359e16),
  X(0x443617f3), X(0x453602eb), X(0x46354b65), X(0x4733dde1),
  X(0x4831a6ff), X(0x492e937f), X(0x4a2a9045), X(0x4b258a5f),
  X(0x4c1f6f06), X(0x4d182ba2), X(0x4e0fadce), X(0x4f05e35b),
  X(0x4ffaba53), X(0x50ee20fd), X(0x51e005e1), X(0x52d057ca),
  X(0x53bf05ca), X(0x54abff3b), X(0x559733c7), X(0x56809365),
  X(0x57680e62), X(0x584d955d), X(0x59311952), X(0x5a128b96),
  X(0x5af1dddd), X(0x5bcf023a), X(0x5ca9eb27), X(0x5d828b81),
  X(0x5e58d68d), X(0x5f2cbffc), X(0x5ffe3be9), X(0x60cd3edf),
  X(0x6199bdda), X(0x6263ae45), X(0x632b0602), X(0x63efbb66),
  X(0x64b1c53f), X(0x65711ad0), X(0x662db3d7), X(0x66e7888d),
  X(0x679e91a5), X(0x6852c84e), X(0x69042635), X(0x69b2a582),
  X(0x6a5e40dd), X(0x6b06f36c), X(0x6bacb8d2), X(0x6c4f8d30),
  X(0x6cef6d26), X(0x6d8c55d4), X(0x6e2644d4), X(0x6ebd3840),
  X(0x6f512ead), X(0x6fe2272e), X(0x7070214f), X(0x70fb1d17),
  X(0x71831b06), X(0x72081c16), X(0x728a21b5), X(0x73092dc8),
  X(0x738542a6), X(0x73fe631b), X(0x74749261), X(0x74e7d421),
  X(0x75582c72), X(0x75c59fd5), X(0x76303333), X(0x7697ebdd),
  X(0x76fccf85), X(0x775ee443), X(0x77be308a), X(0x781abb2e),
  X(0x78748b59), X(0x78cba88e), X(0x79201aa7), X(0x7971e9cd),
  X(0x79c11e79), X(0x7a0dc170), X(0x7a57dbc2), X(0x7a9f76c1),
  X(0x7ae49c07), X(0x7b27556b), X(0x7b67ad02), X(0x7ba5ad1b),
  X(0x7be1603a), X(0x7c1ad118), X(0x7c520a9e), X(0x7c8717e1),
  X(0x7cba0421), X(0x7ceadac3), X(0x7d19a74f), X(0x7d46756e),
  X(0x7d7150e5), X(0x7d9a4592), X(0x7dc15f69), X(0x7de6aa71),
  X(0x7e0a32c0), X(0x7e2c0479), X(0x7e4c2bc7), X(0x7e6ab4db),
  X(0x7e87abe9), X(0x7ea31d24), X(0x7ebd14be), X(0x7ed59edd),
  X(0x7eecc7a3), X(0x7f029b21), X(0x7f17255a), X(0x7f2a723f),
  X(0x7f3c8daa), X(0x7f4d835d), X(0x7f5d5f00), X(0x7f6c2c1b),
  X(0x7f79f617), X(0x7f86c83a), X(0x7f92ada2), X(0x7f9db146),
  X(0x7fa7ddf3), X(0x7fb13e46), X(0x7fb9dcb0), X(0x7fc1c36c),
  X(0x7fc8fc83), X(0x7fcf91c7), X(0x7fd58cd2), X(0x7fdaf702),
  X(0x7fdfd979), X(0x7fe43d1c), X(0x7fe82a8b), X(0x7febaa29),
  X(0x7feec412), X(0x7ff1801c), X(0x7ff3e5d6), X(0x7ff5fc86),
  X(0x7ff7cb29), X(0x7ff9586f), X(0x7ffaaaba), X(0x7ffbc81e),
  X(0x7ffcb660), X(0x7ffd7af3), X(0x7ffe1afa), X(0x7ffe9b42),
  X(0x7fff0047), X(0x7fff4e2f), X(0x7fff88c9), X(0x7fffb390),
  X(0x7fffd1a6), X(0x7fffe5d7), X(0x7ffff296), X(0x7ffff9fd),
  X(0x7ffffdcd), X(0x7fffff6d), X(0x7fffffed), X(0x7fffffff),
};

static LOOKUP_T vwin1024[512] = {
  X(0x00001f02), X(0x0001170e), X(0x00030724), X(0x0005ef40),
  X(0x0009cf59), X(0x000ea767), X(0x0014775e), X(0x001b3f2e),
  X(0x0022fec8), X(0x002bb618), X(0x00356508), X(0x00400b81),
  X(0x004ba968), X(0x00583ea0), X(0x0065cb0a), X(0x00744e84),
  X(0x0083c8ea), X(0x00943a14), X(0x00a5a1da), X(0x00b80010),
  X(0x00cb5488), X(0x00df9f10), X(0x00f4df76), X(0x010b1584),
  X(0x01224101), X(0x013a61b2), X(0x01537759), X(0x016d81b6),
  X(0x01888087), X(0x01a47385), X(0x01c15a69), X(0x01df34e6),
  X(0x01fe02b1), X(0x021dc377), X(0x023e76e7), X(0x02601ca9),
  X(0x0282b466), X(0x02a63dc1), X(0x02cab85d), X(0x02f023d6),
  X(0x03167fcb), X(0x033dcbd3), X(0x03660783), X(0x038f3270),
  X(0x03b94c29), X(0x03e4543a), X(0x04104a2e), X(0x043d2d8b),
  X(0x046afdd5), X(0x0499ba8c), X(0x04c9632d), X(0x04f9f734),
  X(0x052b7615), X(0x055ddf46), X(0x05913237), X(0x05c56e53),
  X(0x05fa9306), X(0x06309fb6), X(0x066793c5), X(0x069f6e93),
  X(0x06d82f7c), X(0x0711d5d9), X(0x074c60fe), X(0x0787d03d),
  X(0x07c422e4), X(0x0801583e), X(0x083f6f91), X(0x087e681f),
  X(0x08be4129), X(0x08fef9ea), X(0x0940919a), X(0x0983076d),
  X(0x09c65a92), X(0x0a0a8a38), X(0x0a4f9585), X(0x0a957b9f),
  X(0x0adc3ba7), X(0x0b23d4b9), X(0x0b6c45ee), X(0x0bb58e5a),
  X(0x0bffad0f), X(0x0c4aa11a), X(0x0c966982), X(0x0ce3054d),
  X(0x0d30737b), X(0x0d7eb308), X(0x0dcdc2eb), X(0x0e1da21a),
  X(0x0e6e4f83), X(0x0ebfca11), X(0x0f1210ad), X(0x0f652238),
  X(0x0fb8fd91), X(0x100da192), X(0x10630d11), X(0x10b93ee0),
  X(0x111035cb), X(0x1167f09a), X(0x11c06e13), X(0x1219acf5),
  X(0x1273abfb), X(0x12ce69db), X(0x1329e54a), X(0x13861cf3),
  X(0x13e30f80), X(0x1440bb97), X(0x149f1fd8), X(0x14fe3ade),
  X(0x155e0b40), X(0x15be8f92), X(0x161fc662), X(0x1681ae38),
  X(0x16e4459b), X(0x17478b0b), X(0x17ab7d03), X(0x181019fb),
  X(0x18756067), X(0x18db4eb3), X(0x1941e34a), X(0x19a91c92),
  X(0x1a10f8ea), X(0x1a7976af), X(0x1ae29439), X(0x1b4c4fda),
  X(0x1bb6a7e2), X(0x1c219a9a), X(0x1c8d2649), X(0x1cf9492e),
  X(0x1d660188), X(0x1dd34d8e), X(0x1e412b74), X(0x1eaf996a),
  X(0x1f1e959b), X(0x1f8e1e2f), X(0x1ffe3146), X(0x206ecd01),
  X(0x20dfef78), X(0x215196c2), X(0x21c3c0f0), X(0x22366c10),
  X(0x22a9962a), X(0x231d3d45), X(0x23915f60), X(0x2405fa7a),
  X(0x247b0c8c), X(0x24f09389), X(0x25668d65), X(0x25dcf80c),
  X(0x2653d167), X(0x26cb175e), X(0x2742c7d0), X(0x27bae09e),
  X(0x28335fa2), X(0x28ac42b3), X(0x292587a5), X(0x299f2c48),
  X(0x2a192e69), X(0x2a938bd1), X(0x2b0e4247), X(0x2b894f8d),
  X(0x2c04b164), X(0x2c806588), X(0x2cfc69b2), X(0x2d78bb9a),
  X(0x2df558f4), X(0x2e723f6f), X(0x2eef6cbb), X(0x2f6cde83),
  X(0x2fea9270), X(0x30688627), X(0x30e6b74e), X(0x31652385),
  X(0x31e3c86b), X(0x3262a39e), X(0x32e1b2b8), X(0x3360f352),
  X(0x33e06303), X(0x345fff5e), X(0x34dfc5f8), X(0x355fb462),
  X(0x35dfc82a), X(0x365ffee0), X(0x36e0560f), X(0x3760cb43),
  X(0x37e15c05), X(0x386205df), X(0x38e2c657), X(0x39639af5),
  X(0x39e4813e), X(0x3a6576b6), X(0x3ae678e3), X(0x3b678547),
  X(0x3be89965), X(0x3c69b2c1), X(0x3ceacedc), X(0x3d6beb37),
  X(0x3ded0557), X(0x3e6e1abb), X(0x3eef28e6), X(0x3f702d5a),
  X(0x3ff1259a), X(0x40720f29), X(0x40f2e789), X(0x4173ac3f),
  X(0x41f45ad0), X(0x4274f0c2), X(0x42f56b9a), X(0x4375c8e0),
  X(0x43f6061d), X(0x447620db), X(0x44f616a5), X(0x4575e509),
  X(0x45f58994), X(0x467501d6), X(0x46f44b62), X(0x477363cb),
  X(0x47f248a6), X(0x4870f78e), X(0x48ef6e1a), X(0x496da9e8),
  X(0x49eba897), X(0x4a6967c8), X(0x4ae6e521), X(0x4b641e47),
  X(0x4be110e5), X(0x4c5dbaa7), X(0x4cda193f), X(0x4d562a5f),
  X(0x4dd1ebbd), X(0x4e4d5b15), X(0x4ec87623), X(0x4f433aa9),
  X(0x4fbda66c), X(0x5037b734), X(0x50b16acf), X(0x512abf0e),
  X(0x51a3b1c5), X(0x521c40ce), X(0x52946a06), X(0x530c2b50),
  X(0x53838292), X(0x53fa6db8), X(0x5470eab3), X(0x54e6f776),
  X(0x555c91fc), X(0x55d1b844), X(0x56466851), X(0x56baa02f),
  X(0x572e5deb), X(0x57a19f98), X(0x58146352), X(0x5886a737),
  X(0x58f8696d), X(0x5969a81c), X(0x59da6177), X(0x5a4a93b4),
  X(0x5aba3d0f), X(0x5b295bcb), X(0x5b97ee30), X(0x5c05f28d),
  X(0x5c736738), X(0x5ce04a8d), X(0x5d4c9aed), X(0x5db856c1),
  X(0x5e237c78), X(0x5e8e0a89), X(0x5ef7ff6f), X(0x5f6159b0),
  X(0x5fca17d4), X(0x6032386e), X(0x6099ba15), X(0x61009b69),
  X(0x6166db11), X(0x61cc77b9), X(0x62317017), X(0x6295c2e7),
  X(0x62f96eec), X(0x635c72f1), X(0x63becdc8), X(0x64207e4b),
  X(0x6481835a), X(0x64e1dbde), X(0x654186c8), X(0x65a0830e),
  X(0x65fecfb1), X(0x665c6bb7), X(0x66b95630), X(0x67158e30),
  X(0x677112d7), X(0x67cbe34b), X(0x6825feb9), X(0x687f6456),
  X(0x68d81361), X(0x69300b1e), X(0x69874ada), X(0x69ddd1ea),
  X(0x6a339fab), X(0x6a88b382), X(0x6add0cdb), X(0x6b30ab2a),
  X(0x6b838dec), X(0x6bd5b4a6), X(0x6c271ee2), X(0x6c77cc36),
  X(0x6cc7bc3d), X(0x6d16ee9b), X(0x6d6562fb), X(0x6db31911),
  X(0x6e001099), X(0x6e4c4955), X(0x6e97c311), X(0x6ee27d9f),
  X(0x6f2c78d9), X(0x6f75b4a2), X(0x6fbe30e4), X(0x7005ed91),
  X(0x704ceaa1), X(0x70932816), X(0x70d8a5f8), X(0x711d6457),
  X(0x7161634b), X(0x71a4a2f3), X(0x71e72375), X(0x7228e500),
  X(0x7269e7c8), X(0x72aa2c0a), X(0x72e9b209), X(0x73287a12),
  X(0x73668476), X(0x73a3d18f), X(0x73e061bc), X(0x741c3566),
  X(0x74574cfa), X(0x7491a8ee), X(0x74cb49be), X(0x75042fec),
  X(0x753c5c03), X(0x7573ce92), X(0x75aa882f), X(0x75e08979),
  X(0x7615d313), X(0x764a65a7), X(0x767e41e5), X(0x76b16884),
  X(0x76e3da40), X(0x771597dc), X(0x7746a221), X(0x7776f9dd),
  X(0x77a69fe6), X(0x77d59514), X(0x7803da49), X(0x7831706a),
  X(0x785e5861), X(0x788a9320), X(0x78b6219c), X(0x78e104cf),
  X(0x790b3dbb), X(0x7934cd64), X(0x795db4d5), X(0x7985f51d),
  X(0x79ad8f50), X(0x79d48486), X(0x79fad5de), X(0x7a208478),
  X(0x7a45917b), X(0x7a69fe12), X(0x7a8dcb6c), X(0x7ab0fabb),
  X(0x7ad38d36), X(0x7af5841a), X(0x7b16e0a3), X(0x7b37a416),
  X(0x7b57cfb8), X(0x7b7764d4), X(0x7b9664b6), X(0x7bb4d0b0),
  X(0x7bd2aa14), X(0x7beff23b), X(0x7c0caa7f), X(0x7c28d43c),
  X(0x7c4470d2), X(0x7c5f81a5), X(0x7c7a081a), X(0x7c940598),
  X(0x7cad7b8b), X(0x7cc66b5e), X(0x7cded680), X(0x7cf6be64),
  X(0x7d0e247b), X(0x7d250a3c), X(0x7d3b711c), X(0x7d515a95),
  X(0x7d66c822), X(0x7d7bbb3c), X(0x7d903563), X(0x7da43814),
  X(0x7db7c4d0), X(0x7dcadd16), X(0x7ddd826a), X(0x7defb64d),
  X(0x7e017a44), X(0x7e12cfd3), X(0x7e23b87f), X(0x7e3435cc),
  X(0x7e444943), X(0x7e53f467), X(0x7e6338c0), X(0x7e7217d5),
  X(0x7e80932b), X(0x7e8eac49), X(0x7e9c64b7), X(0x7ea9bdf8),
  X(0x7eb6b994), X(0x7ec35910), X(0x7ecf9def), X(0x7edb89b6),
  X(0x7ee71de9), X(0x7ef25c09), X(0x7efd4598), X(0x7f07dc16),
  X(0x7f122103), X(0x7f1c15dc), X(0x7f25bc1f), X(0x7f2f1547),
  X(0x7f3822cd), X(0x7f40e62b), X(0x7f4960d6), X(0x7f519443),
  X(0x7f5981e7), X(0x7f612b31), X(0x7f689191), X(0x7f6fb674),
  X(0x7f769b45), X(0x7f7d416c), X(0x7f83aa51), X(0x7f89d757),
  X(0x7f8fc9df), X(0x7f958348), X(0x7f9b04ef), X(0x7fa0502e),
  X(0x7fa56659), X(0x7faa48c7), X(0x7faef8c7), X(0x7fb377a7),
  X(0x7fb7c6b3), X(0x7fbbe732), X(0x7fbfda67), X(0x7fc3a196),
  X(0x7fc73dfa), X(0x7fcab0ce), X(0x7fcdfb4a), X(0x7fd11ea0),
  X(0x7fd41c00), X(0x7fd6f496), X(0x7fd9a989), X(0x7fdc3bff),
  X(0x7fdead17), X(0x7fe0fdee), X(0x7fe32f9d), X(0x7fe54337),
  X(0x7fe739ce), X(0x7fe9146c), X(0x7fead41b), X(0x7fec79dd),
  X(0x7fee06b2), X(0x7fef7b94), X(0x7ff0d97b), X(0x7ff22158),
  X(0x7ff35417), X(0x7ff472a3), X(0x7ff57de0), X(0x7ff676ac),
  X(0x7ff75de3), X(0x7ff8345a), X(0x7ff8fae4), X(0x7ff9b24b),
  X(0x7ffa5b58), X(0x7ffaf6cd), X(0x7ffb8568), X(0x7ffc07e2),
  X(0x7ffc7eed), X(0x7ffceb38), X(0x7ffd4d6d), X(0x7ffda631),
  X(0x7ffdf621), X(0x7ffe3dd8), X(0x7ffe7dea), X(0x7ffeb6e7),
  X(0x7ffee959), X(0x7fff15c4), X(0x7fff3ca9), X(0x7fff5e80),
  X(0x7fff7bc0), X(0x7fff94d6), X(0x7fffaa2d), X(0x7fffbc29),
  X(0x7fffcb29), X(0x7fffd786), X(0x7fffe195), X(0x7fffe9a3),
  X(0x7fffeffa), X(0x7ffff4dd), X(0x7ffff889), X(0x7ffffb37),
  X(0x7ffffd1a), X(0x7ffffe5d), X(0x7fffff29), X(0x7fffffa0),
  X(0x7fffffdd), X(0x7ffffff7), X(0x7fffffff), X(0x7fffffff),
};

static LOOKUP_T vwin2048[1024] = {
  X(0x000007c0), X(0x000045c4), X(0x0000c1ca), X(0x00017bd3),
  X(0x000273de), X(0x0003a9eb), X(0x00051df9), X(0x0006d007),
  X(0x0008c014), X(0x000aee1e), X(0x000d5a25), X(0x00100428),
  X(0x0012ec23), X(0x00161216), X(0x001975fe), X(0x001d17da),
  X(0x0020f7a8), X(0x00251564), X(0x0029710c), X(0x002e0a9e),
  X(0x0032e217), X(0x0037f773), X(0x003d4ab0), X(0x0042dbca),
  X(0x0048aabe), X(0x004eb788), X(0x00550224), X(0x005b8a8f),
  X(0x006250c5), X(0x006954c1), X(0x0070967e), X(0x007815f9),
  X(0x007fd32c), X(0x0087ce13), X(0x009006a9), X(0x00987ce9),
  X(0x00a130cc), X(0x00aa224f), X(0x00b3516b), X(0x00bcbe1a),
  X(0x00c66856), X(0x00d0501a), X(0x00da755f), X(0x00e4d81f),
  X(0x00ef7853), X(0x00fa55f4), X(0x010570fc), X(0x0110c963),
  X(0x011c5f22), X(0x01283232), X(0x0134428c), X(0x01409027),
  X(0x014d1afb), X(0x0159e302), X(0x0166e831), X(0x01742a82),
  X(0x0181a9ec), X(0x018f6665), X(0x019d5fe5), X(0x01ab9663),
  X(0x01ba09d6), X(0x01c8ba34), X(0x01d7a775), X(0x01e6d18d),
  X(0x01f63873), X(0x0205dc1e), X(0x0215bc82), X(0x0225d997),
  X(0x02363350), X(0x0246c9a3), X(0x02579c86), X(0x0268abed),
  X(0x0279f7cc), X(0x028b801a), X(0x029d44c9), X(0x02af45ce),
  X(0x02c1831d), X(0x02d3fcaa), X(0x02e6b269), X(0x02f9a44c),
  X(0x030cd248), X(0x03203c4f), X(0x0333e255), X(0x0347c44b),
  X(0x035be225), X(0x03703bd5), X(0x0384d14d), X(0x0399a280),
  X(0x03aeaf5e), X(0x03c3f7d9), X(0x03d97be4), X(0x03ef3b6e),
  X(0x0405366a), X(0x041b6cc8), X(0x0431de78), X(0x04488b6c),
  X(0x045f7393), X(0x047696dd), X(0x048df53b), X(0x04a58e9b),
  X(0x04bd62ee), X(0x04d57223), X(0x04edbc28), X(0x050640ed),
  X(0x051f0060), X(0x0537fa70), X(0x05512f0a), X(0x056a9e1e),
  X(0x05844798), X(0x059e2b67), X(0x05b84978), X(0x05d2a1b8),
  X(0x05ed3414), X(0x06080079), X(0x062306d3), X(0x063e470f),
  X(0x0659c119), X(0x067574dd), X(0x06916247), X(0x06ad8941),
  X(0x06c9e9b8), X(0x06e68397), X(0x070356c8), X(0x07206336),
  X(0x073da8cb), X(0x075b2772), X(0x0778df15), X(0x0796cf9c),
  X(0x07b4f8f3), X(0x07d35b01), X(0x07f1f5b1), X(0x0810c8eb),
  X(0x082fd497), X(0x084f189e), X(0x086e94e9), X(0x088e495e),
  X(0x08ae35e6), X(0x08ce5a68), X(0x08eeb6cc), X(0x090f4af8),
  X(0x093016d3), X(0x09511a44), X(0x09725530), X(0x0993c77f),
  X(0x09b57115), X(0x09d751d8), X(0x09f969ae), X(0x0a1bb87c),
  X(0x0a3e3e26), X(0x0a60fa91), X(0x0a83eda2), X(0x0aa7173c),
  X(0x0aca7743), X(0x0aee0d9b), X(0x0b11da28), X(0x0b35dccc),
  X(0x0b5a156a), X(0x0b7e83e5), X(0x0ba3281f), X(0x0bc801fa),
  X(0x0bed1159), X(0x0c12561c), X(0x0c37d025), X(0x0c5d7f55),
  X(0x0c83638d), X(0x0ca97cae), X(0x0ccfca97), X(0x0cf64d2a),
  X(0x0d1d0444), X(0x0d43efc7), X(0x0d6b0f92), X(0x0d926383),
  X(0x0db9eb79), X(0x0de1a752), X(0x0e0996ee), X(0x0e31ba29),
  X(0x0e5a10e2), X(0x0e829af6), X(0x0eab5841), X(0x0ed448a2),
  X(0x0efd6bf4), X(0x0f26c214), X(0x0f504ade), X(0x0f7a062e),
  X(0x0fa3f3df), X(0x0fce13cd), X(0x0ff865d2), X(0x1022e9ca),
  X(0x104d9f8e), X(0x107886f9), X(0x10a39fe5), X(0x10ceea2c),
  X(0x10fa65a6), X(0x1126122d), X(0x1151ef9a), X(0x117dfdc5),
  X(0x11aa3c87), X(0x11d6abb6), X(0x12034b2c), X(0x12301ac0),
  X(0x125d1a48), X(0x128a499b), X(0x12b7a891), X(0x12e536ff),
  X(0x1312f4bb), X(0x1340e19c), X(0x136efd75), X(0x139d481e),
  X(0x13cbc16a), X(0x13fa692f), X(0x14293f40), X(0x14584371),
  X(0x14877597), X(0x14b6d585), X(0x14e6630d), X(0x15161e04),
  X(0x1546063b), X(0x15761b85), X(0x15a65db3), X(0x15d6cc99),
  X(0x16076806), X(0x16382fcd), X(0x166923bf), X(0x169a43ab),
  X(0x16cb8f62), X(0x16fd06b5), X(0x172ea973), X(0x1760776b),
  X(0x1792706e), X(0x17c49449), X(0x17f6e2cb), X(0x18295bc3),
  X(0x185bfeff), X(0x188ecc4c), X(0x18c1c379), X(0x18f4e452),
  X(0x19282ea4), X(0x195ba23c), X(0x198f3ee6), X(0x19c3046e),
  X(0x19f6f2a1), X(0x1a2b094a), X(0x1a5f4833), X(0x1a93af28),
  X(0x1ac83df3), X(0x1afcf460), X(0x1b31d237), X(0x1b66d744),
  X(0x1b9c034e), X(0x1bd15621), X(0x1c06cf84), X(0x1c3c6f40),
  X(0x1c72351e), X(0x1ca820e6), X(0x1cde3260), X(0x1d146953),
  X(0x1d4ac587), X(0x1d8146c3), X(0x1db7eccd), X(0x1deeb76c),
  X(0x1e25a667), X(0x1e5cb982), X(0x1e93f085), X(0x1ecb4b33),
  X(0x1f02c953), X(0x1f3a6aaa), X(0x1f722efb), X(0x1faa160b),
  X(0x1fe21f9e), X(0x201a4b79), X(0x2052995d), X(0x208b0910),
  X(0x20c39a53), X(0x20fc4cea), X(0x21352097), X(0x216e151c),
  X(0x21a72a3a), X(0x21e05fb5), X(0x2219b54d), X(0x22532ac3),
  X(0x228cbfd8), X(0x22c6744d), X(0x230047e2), X(0x233a3a58),
  X(0x23744b6d), X(0x23ae7ae3), X(0x23e8c878), X(0x242333ec),
  X(0x245dbcfd), X(0x24986369), X(0x24d326f1), X(0x250e0750),
  X(0x25490446), X(0x25841d90), X(0x25bf52ec), X(0x25faa417),
  X(0x263610cd), X(0x267198cc), X(0x26ad3bcf), X(0x26e8f994),
  X(0x2724d1d6), X(0x2760c451), X(0x279cd0c0), X(0x27d8f6e0),
  X(0x2815366a), X(0x28518f1b), X(0x288e00ac), X(0x28ca8ad8),
  X(0x29072d5a), X(0x2943e7eb), X(0x2980ba45), X(0x29bda422),
  X(0x29faa53c), X(0x2a37bd4a), X(0x2a74ec07), X(0x2ab2312b),
  X(0x2aef8c6f), X(0x2b2cfd8b), X(0x2b6a8437), X(0x2ba8202c),
  X(0x2be5d120), X(0x2c2396cc), X(0x2c6170e7), X(0x2c9f5f29),
  X(0x2cdd6147), X(0x2d1b76fa), X(0x2d599ff7), X(0x2d97dbf5),
  X(0x2dd62aab), X(0x2e148bcf), X(0x2e52ff16), X(0x2e918436),
  X(0x2ed01ae5), X(0x2f0ec2d9), X(0x2f4d7bc6), X(0x2f8c4562),
  X(0x2fcb1f62), X(0x300a097a), X(0x3049035f), X(0x30880cc6),
  X(0x30c72563), X(0x31064cea), X(0x3145830f), X(0x3184c786),
  X(0x31c41a03), X(0x32037a39), X(0x3242e7dc), X(0x3282629f),
  X(0x32c1ea36), X(0x33017e53), X(0x33411ea9), X(0x3380caec),
  X(0x33c082ce), X(0x34004602), X(0x34401439), X(0x347fed27),
  X(0x34bfd07e), X(0x34ffbdf0), X(0x353fb52e), X(0x357fb5ec),
  X(0x35bfbfda), X(0x35ffd2aa), X(0x363fee0f), X(0x368011b9),
  X(0x36c03d5a), X(0x370070a4), X(0x3740ab48), X(0x3780ecf7),
  X(0x37c13562), X(0x3801843a), X(0x3841d931), X(0x388233f7),
  X(0x38c2943d), X(0x3902f9b4), X(0x3943640d), X(0x3983d2f8),
  X(0x39c44626), X(0x3a04bd48), X(0x3a45380e), X(0x3a85b62a),
  X(0x3ac6374a), X(0x3b06bb20), X(0x3b47415c), X(0x3b87c9ae),
  X(0x3bc853c7), X(0x3c08df57), X(0x3c496c0f), X(0x3c89f99f),
  X(0x3cca87b6), X(0x3d0b1605), X(0x3d4ba43d), X(0x3d8c320e),
  X(0x3dccbf27), X(0x3e0d4b3a), X(0x3e4dd5f6), X(0x3e8e5f0c),
  X(0x3ecee62b), X(0x3f0f6b05), X(0x3f4fed49), X(0x3f906ca8),
  X(0x3fd0e8d2), X(0x40116177), X(0x4051d648), X(0x409246f6),
  X(0x40d2b330), X(0x41131aa7), X(0x41537d0c), X(0x4193da10),
  X(0x41d43162), X(0x421482b4), X(0x4254cdb7), X(0x4295121b),
  X(0x42d54f91), X(0x431585ca), X(0x4355b477), X(0x4395db49),
  X(0x43d5f9f1), X(0x44161021), X(0x44561d8a), X(0x449621dd),
  X(0x44d61ccc), X(0x45160e08), X(0x4555f544), X(0x4595d230),
  X(0x45d5a47f), X(0x46156be3), X(0x4655280e), X(0x4694d8b2),
  X(0x46d47d82), X(0x4714162f), X(0x4753a26d), X(0x479321ef),
  X(0x47d29466), X(0x4811f987), X(0x48515104), X(0x48909a91),
  X(0x48cfd5e1), X(0x490f02a7), X(0x494e2098), X(0x498d2f66),
  X(0x49cc2ec7), X(0x4a0b1e6f), X(0x4a49fe11), X(0x4a88cd62),
  X(0x4ac78c18), X(0x4b0639e6), X(0x4b44d683), X(0x4b8361a2),
  X(0x4bc1dafa), X(0x4c004241), X(0x4c3e972c), X(0x4c7cd970),
  X(0x4cbb08c5), X(0x4cf924e1), X(0x4d372d7a), X(0x4d752247),
  X(0x4db30300), X(0x4df0cf5a), X(0x4e2e870f), X(0x4e6c29d6),
  X(0x4ea9b766), X(0x4ee72f78), X(0x4f2491c4), X(0x4f61de02),
  X(0x4f9f13ec), X(0x4fdc333b), X(0x50193ba8), X(0x50562ced),
  X(0x509306c3), X(0x50cfc8e5), X(0x510c730d), X(0x514904f6),
  X(0x51857e5a), X(0x51c1def5), X(0x51fe2682), X(0x523a54bc),
  X(0x52766961), X(0x52b2642c), X(0x52ee44d9), X(0x532a0b26),
  X(0x5365b6d0), X(0x53a14793), X(0x53dcbd2f), X(0x54181760),
  X(0x545355e5), X(0x548e787d), X(0x54c97ee6), X(0x550468e1),
  X(0x553f362c), X(0x5579e687), X(0x55b479b3), X(0x55eeef70),
  X(0x5629477f), X(0x566381a1), X(0x569d9d97), X(0x56d79b24),
  X(0x57117a0a), X(0x574b3a0a), X(0x5784dae9), X(0x57be5c69),
  X(0x57f7be4d), X(0x5831005a), X(0x586a2254), X(0x58a32400),
  X(0x58dc0522), X(0x5914c57f), X(0x594d64de), X(0x5985e305),
  X(0x59be3fba), X(0x59f67ac3), X(0x5a2e93e9), X(0x5a668af2),
  X(0x5a9e5fa6), X(0x5ad611ce), X(0x5b0da133), X(0x5b450d9d),
  X(0x5b7c56d7), X(0x5bb37ca9), X(0x5bea7ede), X(0x5c215d41),
  X(0x5c58179d), X(0x5c8eadbe), X(0x5cc51f6f), X(0x5cfb6c7c),
  X(0x5d3194b2), X(0x5d6797de), X(0x5d9d75cf), X(0x5dd32e51),
  X(0x5e08c132), X(0x5e3e2e43), X(0x5e737551), X(0x5ea8962d),
  X(0x5edd90a7), X(0x5f12648e), X(0x5f4711b4), X(0x5f7b97ea),
  X(0x5faff702), X(0x5fe42ece), X(0x60183f20), X(0x604c27cc),
  X(0x607fe8a6), X(0x60b38180), X(0x60e6f22f), X(0x611a3a89),
  X(0x614d5a62), X(0x61805190), X(0x61b31fe9), X(0x61e5c545),
  X(0x62184179), X(0x624a945d), X(0x627cbdca), X(0x62aebd98),
  X(0x62e0939f), X(0x63123fba), X(0x6343c1c1), X(0x6375198f),
  X(0x63a646ff), X(0x63d749ec), X(0x64082232), X(0x6438cfad),
  X(0x64695238), X(0x6499a9b3), X(0x64c9d5f9), X(0x64f9d6ea),
  X(0x6529ac63), X(0x65595643), X(0x6588d46a), X(0x65b826b8),
  X(0x65e74d0e), X(0x6616474b), X(0x66451552), X(0x6673b704),
  X(0x66a22c44), X(0x66d074f4), X(0x66fe90f8), X(0x672c8033),
  X(0x675a428a), X(0x6787d7e1), X(0x67b5401f), X(0x67e27b27),
  X(0x680f88e1), X(0x683c6934), X(0x68691c05), X(0x6895a13e),
  X(0x68c1f8c7), X(0x68ee2287), X(0x691a1e68), X(0x6945ec54),
  X(0x69718c35), X(0x699cfdf5), X(0x69c8417f), X(0x69f356c0),
  X(0x6a1e3da3), X(0x6a48f615), X(0x6a738002), X(0x6a9ddb5a),
  X(0x6ac80808), X(0x6af205fd), X(0x6b1bd526), X(0x6b457575),
  X(0x6b6ee6d8), X(0x6b982940), X(0x6bc13c9f), X(0x6bea20e5),
  X(0x6c12d605), X(0x6c3b5bf1), X(0x6c63b29c), X(0x6c8bd9fb),
  X(0x6cb3d200), X(0x6cdb9aa0), X(0x6d0333d0), X(0x6d2a9d86),
  X(0x6d51d7b7), X(0x6d78e25a), X(0x6d9fbd67), X(0x6dc668d3),
  X(0x6dece498), X(0x6e1330ad), X(0x6e394d0c), X(0x6e5f39ae),
  X(0x6e84f68d), X(0x6eaa83a2), X(0x6ecfe0ea), X(0x6ef50e5e),
  X(0x6f1a0bfc), X(0x6f3ed9bf), X(0x6f6377a4), X(0x6f87e5a8),
  X(0x6fac23c9), X(0x6fd03206), X(0x6ff4105c), X(0x7017becc),
  X(0x703b3d54), X(0x705e8bf5), X(0x7081aaaf), X(0x70a49984),
  X(0x70c75874), X(0x70e9e783), X(0x710c46b2), X(0x712e7605),
  X(0x7150757f), X(0x71724523), X(0x7193e4f6), X(0x71b554fd),
  X(0x71d6953e), X(0x71f7a5bd), X(0x72188681), X(0x72393792),
  X(0x7259b8f5), X(0x727a0ab2), X(0x729a2cd2), X(0x72ba1f5d),
  X(0x72d9e25c), X(0x72f975d8), X(0x7318d9db), X(0x73380e6f),
  X(0x735713a0), X(0x7375e978), X(0x73949003), X(0x73b3074c),
  X(0x73d14f61), X(0x73ef684f), X(0x740d5222), X(0x742b0ce9),
  X(0x744898b1), X(0x7465f589), X(0x74832381), X(0x74a022a8),
  X(0x74bcf30e), X(0x74d994c3), X(0x74f607d8), X(0x75124c5f),
  X(0x752e6268), X(0x754a4a05), X(0x7566034b), X(0x75818e4a),
  X(0x759ceb16), X(0x75b819c4), X(0x75d31a66), X(0x75eded12),
  X(0x760891dc), X(0x762308da), X(0x763d5221), X(0x76576dc8),
  X(0x76715be4), X(0x768b1c8c), X(0x76a4afd9), X(0x76be15e0),
  X(0x76d74ebb), X(0x76f05a82), X(0x7709394d), X(0x7721eb35),
  X(0x773a7054), X(0x7752c8c4), X(0x776af49f), X(0x7782f400),
  X(0x779ac701), X(0x77b26dbd), X(0x77c9e851), X(0x77e136d8),
  X(0x77f8596f), X(0x780f5032), X(0x78261b3f), X(0x783cbab2),
  X(0x78532eaa), X(0x78697745), X(0x787f94a0), X(0x789586db),
  X(0x78ab4e15), X(0x78c0ea6d), X(0x78d65c03), X(0x78eba2f7),
  X(0x7900bf68), X(0x7915b179), X(0x792a7949), X(0x793f16fb),
  X(0x79538aaf), X(0x7967d488), X(0x797bf4a8), X(0x798feb31),
  X(0x79a3b846), X(0x79b75c0a), X(0x79cad6a1), X(0x79de282e),
  X(0x79f150d5), X(0x7a0450bb), X(0x7a172803), X(0x7a29d6d3),
  X(0x7a3c5d50), X(0x7a4ebb9f), X(0x7a60f1e6), X(0x7a73004a),
  X(0x7a84e6f2), X(0x7a96a604), X(0x7aa83da7), X(0x7ab9ae01),
  X(0x7acaf73a), X(0x7adc1979), X(0x7aed14e6), X(0x7afde9a8),
  X(0x7b0e97e8), X(0x7b1f1fcd), X(0x7b2f8182), X(0x7b3fbd2d),
  X(0x7b4fd2f9), X(0x7b5fc30f), X(0x7b6f8d98), X(0x7b7f32bd),
  X(0x7b8eb2a9), X(0x7b9e0d85), X(0x7bad437d), X(0x7bbc54b9),
  X(0x7bcb4166), X(0x7bda09ae), X(0x7be8adbc), X(0x7bf72dbc),
  X(0x7c0589d8), X(0x7c13c23d), X(0x7c21d716), X(0x7c2fc88f),
  X(0x7c3d96d5), X(0x7c4b4214), X(0x7c58ca78), X(0x7c66302d),
  X(0x7c737362), X(0x7c809443), X(0x7c8d92fc), X(0x7c9a6fbc),
  X(0x7ca72aaf), X(0x7cb3c404), X(0x7cc03be8), X(0x7ccc9288),
  X(0x7cd8c814), X(0x7ce4dcb9), X(0x7cf0d0a5), X(0x7cfca406),
  X(0x7d08570c), X(0x7d13e9e5), X(0x7d1f5cbf), X(0x7d2aafca),
  X(0x7d35e335), X(0x7d40f72e), X(0x7d4bebe4), X(0x7d56c188),
  X(0x7d617848), X(0x7d6c1054), X(0x7d7689db), X(0x7d80e50e),
  X(0x7d8b221b), X(0x7d954133), X(0x7d9f4286), X(0x7da92643),
  X(0x7db2ec9b), X(0x7dbc95bd), X(0x7dc621da), X(0x7dcf9123),
  X(0x7dd8e3c6), X(0x7de219f6), X(0x7deb33e2), X(0x7df431ba),
  X(0x7dfd13af), X(0x7e05d9f2), X(0x7e0e84b4), X(0x7e171424),
  X(0x7e1f8874), X(0x7e27e1d4), X(0x7e302074), X(0x7e384487),
  X(0x7e404e3c), X(0x7e483dc4), X(0x7e501350), X(0x7e57cf11),
  X(0x7e5f7138), X(0x7e66f9f4), X(0x7e6e6979), X(0x7e75bff5),
  X(0x7e7cfd9a), X(0x7e842298), X(0x7e8b2f22), X(0x7e922366),
  X(0x7e98ff97), X(0x7e9fc3e4), X(0x7ea6707f), X(0x7ead0598),
  X(0x7eb38360), X(0x7eb9ea07), X(0x7ec039bf), X(0x7ec672b7),
  X(0x7ecc9521), X(0x7ed2a12c), X(0x7ed8970a), X(0x7ede76ea),
  X(0x7ee440fd), X(0x7ee9f573), X(0x7eef947d), X(0x7ef51e4b),
  X(0x7efa930d), X(0x7efff2f2), X(0x7f053e2b), X(0x7f0a74e8),
  X(0x7f0f9758), X(0x7f14a5ac), X(0x7f19a013), X(0x7f1e86bc),
  X(0x7f2359d8), X(0x7f281995), X(0x7f2cc623), X(0x7f315fb1),
  X(0x7f35e66e), X(0x7f3a5a8a), X(0x7f3ebc33), X(0x7f430b98),
  X(0x7f4748e7), X(0x7f4b7450), X(0x7f4f8e01), X(0x7f539629),
  X(0x7f578cf5), X(0x7f5b7293), X(0x7f5f4732), X(0x7f630b00),
  X(0x7f66be2b), X(0x7f6a60df), X(0x7f6df34b), X(0x7f71759b),
  X(0x7f74e7fe), X(0x7f784aa0), X(0x7f7b9daf), X(0x7f7ee156),
  X(0x7f8215c3), X(0x7f853b22), X(0x7f88519f), X(0x7f8b5967),
  X(0x7f8e52a6), X(0x7f913d87), X(0x7f941a36), X(0x7f96e8df),
  X(0x7f99a9ad), X(0x7f9c5ccb), X(0x7f9f0265), X(0x7fa19aa5),
  X(0x7fa425b5), X(0x7fa6a3c1), X(0x7fa914f3), X(0x7fab7974),
  X(0x7fadd16f), X(0x7fb01d0d), X(0x7fb25c78), X(0x7fb48fd9),
  X(0x7fb6b75a), X(0x7fb8d323), X(0x7fbae35d), X(0x7fbce831),
  X(0x7fbee1c7), X(0x7fc0d047), X(0x7fc2b3d9), X(0x7fc48ca5),
  X(0x7fc65ad3), X(0x7fc81e88), X(0x7fc9d7ee), X(0x7fcb872a),
  X(0x7fcd2c63), X(0x7fcec7bf), X(0x7fd05966), X(0x7fd1e17c),
  X(0x7fd36027), X(0x7fd4d58d), X(0x7fd641d3), X(0x7fd7a51e),
  X(0x7fd8ff94), X(0x7fda5157), X(0x7fdb9a8e), X(0x7fdcdb5b),
  X(0x7fde13e2), X(0x7fdf4448), X(0x7fe06caf), X(0x7fe18d3b),
  X(0x7fe2a60e), X(0x7fe3b74b), X(0x7fe4c114), X(0x7fe5c38b),
  X(0x7fe6bed2), X(0x7fe7b30a), X(0x7fe8a055), X(0x7fe986d4),
  X(0x7fea66a7), X(0x7feb3ff0), X(0x7fec12cd), X(0x7fecdf5f),
  X(0x7feda5c5), X(0x7fee6620), X(0x7fef208d), X(0x7fefd52c),
  X(0x7ff0841c), X(0x7ff12d7a), X(0x7ff1d164), X(0x7ff26ff9),
  X(0x7ff30955), X(0x7ff39d96), X(0x7ff42cd9), X(0x7ff4b739),
  X(0x7ff53cd4), X(0x7ff5bdc5), X(0x7ff63a28), X(0x7ff6b217),
  X(0x7ff725af), X(0x7ff7950a), X(0x7ff80043), X(0x7ff86773),
  X(0x7ff8cab4), X(0x7ff92a21), X(0x7ff985d1), X(0x7ff9dddf),
  X(0x7ffa3262), X(0x7ffa8374), X(0x7ffad12c), X(0x7ffb1ba1),
  X(0x7ffb62ec), X(0x7ffba723), X(0x7ffbe85c), X(0x7ffc26b0),
  X(0x7ffc6233), X(0x7ffc9afb), X(0x7ffcd11e), X(0x7ffd04b1),
  X(0x7ffd35c9), X(0x7ffd647b), X(0x7ffd90da), X(0x7ffdbafa),
  X(0x7ffde2f0), X(0x7ffe08ce), X(0x7ffe2ca7), X(0x7ffe4e8e),
  X(0x7ffe6e95), X(0x7ffe8cce), X(0x7ffea94a), X(0x7ffec41b),
  X(0x7ffedd52), X(0x7ffef4ff), X(0x7fff0b33), X(0x7fff1ffd),
  X(0x7fff336e), X(0x7fff4593), X(0x7fff567d), X(0x7fff663a),
  X(0x7fff74d8), X(0x7fff8265), X(0x7fff8eee), X(0x7fff9a81),
  X(0x7fffa52b), X(0x7fffaef8), X(0x7fffb7f5), X(0x7fffc02d),
  X(0x7fffc7ab), X(0x7fffce7c), X(0x7fffd4a9), X(0x7fffda3e),
  X(0x7fffdf44), X(0x7fffe3c6), X(0x7fffe7cc), X(0x7fffeb60),
  X(0x7fffee8a), X(0x7ffff153), X(0x7ffff3c4), X(0x7ffff5e3),
  X(0x7ffff7b8), X(0x7ffff94b), X(0x7ffffaa1), X(0x7ffffbc1),
  X(0x7ffffcb2), X(0x7ffffd78), X(0x7ffffe19), X(0x7ffffe9a),
  X(0x7ffffeff), X(0x7fffff4e), X(0x7fffff89), X(0x7fffffb3),
  X(0x7fffffd2), X(0x7fffffe6), X(0x7ffffff3), X(0x7ffffffa),
  X(0x7ffffffe), X(0x7fffffff), X(0x7fffffff), X(0x7fffffff),
};

static LOOKUP_T vwin4096[2048] = {
  X(0x000001f0), X(0x00001171), X(0x00003072), X(0x00005ef5),
  X(0x00009cf8), X(0x0000ea7c), X(0x00014780), X(0x0001b405),
  X(0x0002300b), X(0x0002bb91), X(0x00035698), X(0x0004011e),
  X(0x0004bb25), X(0x000584ac), X(0x00065db3), X(0x0007463a),
  X(0x00083e41), X(0x000945c7), X(0x000a5ccc), X(0x000b8350),
  X(0x000cb954), X(0x000dfed7), X(0x000f53d8), X(0x0010b857),
  X(0x00122c55), X(0x0013afd1), X(0x001542ca), X(0x0016e541),
  X(0x00189735), X(0x001a58a7), X(0x001c2995), X(0x001e09ff),
  X(0x001ff9e6), X(0x0021f948), X(0x00240826), X(0x00262680),
  X(0x00285454), X(0x002a91a3), X(0x002cde6c), X(0x002f3aaf),
  X(0x0031a66b), X(0x003421a0), X(0x0036ac4f), X(0x00394675),
  X(0x003bf014), X(0x003ea92a), X(0x004171b7), X(0x004449bb),
  X(0x00473135), X(0x004a2824), X(0x004d2e8a), X(0x00504463),
  X(0x005369b2), X(0x00569e74), X(0x0059e2aa), X(0x005d3652),
  X(0x0060996d), X(0x00640bf9), X(0x00678df7), X(0x006b1f66),
  X(0x006ec045), X(0x00727093), X(0x00763051), X(0x0079ff7d),
  X(0x007dde16), X(0x0081cc1d), X(0x0085c991), X(0x0089d671),
  X(0x008df2bc), X(0x00921e71), X(0x00965991), X(0x009aa41a),
  X(0x009efe0c), X(0x00a36766), X(0x00a7e028), X(0x00ac6850),
  X(0x00b0ffde), X(0x00b5a6d1), X(0x00ba5d28), X(0x00bf22e4),
  X(0x00c3f802), X(0x00c8dc83), X(0x00cdd065), X(0x00d2d3a8),
  X(0x00d7e64a), X(0x00dd084c), X(0x00e239ac), X(0x00e77a69),
  X(0x00ecca83), X(0x00f229f9), X(0x00f798ca), X(0x00fd16f5),
  X(0x0102a479), X(0x01084155), X(0x010ded89), X(0x0113a913),
  X(0x011973f3), X(0x011f4e27), X(0x012537af), X(0x012b308a),
  X(0x013138b7), X(0x01375035), X(0x013d7702), X(0x0143ad1f),
  X(0x0149f289), X(0x01504741), X(0x0156ab44), X(0x015d1e92),
  X(0x0163a12a), X(0x016a330b), X(0x0170d433), X(0x017784a3),
  X(0x017e4458), X(0x01851351), X(0x018bf18e), X(0x0192df0d),
  X(0x0199dbcd), X(0x01a0e7cd), X(0x01a8030c), X(0x01af2d89),
  X(0x01b66743), X(0x01bdb038), X(0x01c50867), X(0x01cc6fd0),
  X(0x01d3e670), X(0x01db6c47), X(0x01e30153), X(0x01eaa593),
  X(0x01f25907), X(0x01fa1bac), X(0x0201ed81), X(0x0209ce86),
  X(0x0211beb8), X(0x0219be17), X(0x0221cca2), X(0x0229ea56),
  X(0x02321733), X(0x023a5337), X(0x02429e60), X(0x024af8af),
  X(0x02536220), X(0x025bdab3), X(0x02646267), X(0x026cf93a),
  X(0x02759f2a), X(0x027e5436), X(0x0287185d), X(0x028feb9d),
  X(0x0298cdf4), X(0x02a1bf62), X(0x02aabfe5), X(0x02b3cf7b),
  X(0x02bcee23), X(0x02c61bdb), X(0x02cf58a2), X(0x02d8a475),
  X(0x02e1ff55), X(0x02eb693e), X(0x02f4e230), X(0x02fe6a29),
  X(0x03080127), X(0x0311a729), X(0x031b5c2d), X(0x03252031),
  X(0x032ef334), X(0x0338d534), X(0x0342c630), X(0x034cc625),
  X(0x0356d512), X(0x0360f2f6), X(0x036b1fce), X(0x03755b99),
  X(0x037fa655), X(0x038a0001), X(0x0394689a), X(0x039ee020),
  X(0x03a9668f), X(0x03b3fbe6), X(0x03bea024), X(0x03c95347),
  X(0x03d4154d), X(0x03dee633), X(0x03e9c5f9), X(0x03f4b49b),
  X(0x03ffb219), X(0x040abe71), X(0x0415d9a0), X(0x042103a5),
  X(0x042c3c7d), X(0x04378428), X(0x0442daa2), X(0x044e3fea),
  X(0x0459b3fd), X(0x046536db), X(0x0470c880), X(0x047c68eb),
  X(0x0488181a), X(0x0493d60b), X(0x049fa2bc), X(0x04ab7e2a),
  X(0x04b76854), X(0x04c36137), X(0x04cf68d1), X(0x04db7f21),
  X(0x04e7a424), X(0x04f3d7d8), X(0x05001a3b), X(0x050c6b4a),
  X(0x0518cb04), X(0x05253966), X(0x0531b66e), X(0x053e421a),
  X(0x054adc68), X(0x05578555), X(0x05643cdf), X(0x05710304),
  X(0x057dd7c1), X(0x058abb15), X(0x0597acfd), X(0x05a4ad76),
  X(0x05b1bc7f), X(0x05beda14), X(0x05cc0635), X(0x05d940dd),
  X(0x05e68a0b), X(0x05f3e1bd), X(0x060147f0), X(0x060ebca1),
  X(0x061c3fcf), X(0x0629d176), X(0x06377194), X(0x06452027),
  X(0x0652dd2c), X(0x0660a8a2), X(0x066e8284), X(0x067c6ad1),
  X(0x068a6186), X(0x069866a1), X(0x06a67a1e), X(0x06b49bfc),
  X(0x06c2cc38), X(0x06d10acf), X(0x06df57bf), X(0x06edb304),
  X(0x06fc1c9d), X(0x070a9487), X(0x07191abe), X(0x0727af40),
  X(0x0736520b), X(0x0745031c), X(0x0753c270), X(0x07629004),
  X(0x07716bd6), X(0x078055e2), X(0x078f4e26), X(0x079e549f),
  X(0x07ad694b), X(0x07bc8c26), X(0x07cbbd2e), X(0x07dafc5f),
  X(0x07ea49b7), X(0x07f9a533), X(0x08090ed1), X(0x0818868c),
  X(0x08280c62), X(0x0837a051), X(0x08474255), X(0x0856f26b),
  X(0x0866b091), X(0x08767cc3), X(0x088656fe), X(0x08963f3f),
  X(0x08a63584), X(0x08b639c8), X(0x08c64c0a), X(0x08d66c45),
  X(0x08e69a77), X(0x08f6d69d), X(0x090720b3), X(0x091778b7),
  X(0x0927dea5), X(0x0938527a), X(0x0948d433), X(0x095963cc),
  X(0x096a0143), X(0x097aac94), X(0x098b65bb), X(0x099c2cb6),
  X(0x09ad0182), X(0x09bde41a), X(0x09ced47d), X(0x09dfd2a5),
  X(0x09f0de90), X(0x0a01f83b), X(0x0a131fa3), X(0x0a2454c3),
  X(0x0a359798), X(0x0a46e820), X(0x0a584656), X(0x0a69b237),
  X(0x0a7b2bc0), X(0x0a8cb2ec), X(0x0a9e47ba), X(0x0aafea24),
  X(0x0ac19a29), X(0x0ad357c3), X(0x0ae522ef), X(0x0af6fbab),
  X(0x0b08e1f1), X(0x0b1ad5c0), X(0x0b2cd712), X(0x0b3ee5e5),
  X(0x0b510234), X(0x0b632bfd), X(0x0b75633b), X(0x0b87a7eb),
  X(0x0b99fa08), X(0x0bac5990), X(0x0bbec67e), X(0x0bd140cf),
  X(0x0be3c87e), X(0x0bf65d89), X(0x0c08ffeb), X(0x0c1bafa1),
  X(0x0c2e6ca6), X(0x0c4136f6), X(0x0c540e8f), X(0x0c66f36c),
  X(0x0c79e588), X(0x0c8ce4e1), X(0x0c9ff172), X(0x0cb30b37),
  X(0x0cc6322c), X(0x0cd9664d), X(0x0ceca797), X(0x0cfff605),
  X(0x0d135193), X(0x0d26ba3d), X(0x0d3a2fff), X(0x0d4db2d5),
  X(0x0d6142ba), X(0x0d74dfac), X(0x0d8889a5), X(0x0d9c40a1),
  X(0x0db0049d), X(0x0dc3d593), X(0x0dd7b380), X(0x0deb9e60),
  X(0x0dff962f), X(0x0e139ae7), X(0x0e27ac85), X(0x0e3bcb05),
  X(0x0e4ff662), X(0x0e642e98), X(0x0e7873a2), X(0x0e8cc57d),
  X(0x0ea12423), X(0x0eb58f91), X(0x0eca07c2), X(0x0ede8cb1),
  X(0x0ef31e5b), X(0x0f07bcba), X(0x0f1c67cb), X(0x0f311f88),
  X(0x0f45e3ee), X(0x0f5ab4f7), X(0x0f6f92a0), X(0x0f847ce3),
  X(0x0f9973bc), X(0x0fae7726), X(0x0fc3871e), X(0x0fd8a39d),
  X(0x0fedcca1), X(0x10030223), X(0x1018441f), X(0x102d9291),
  X(0x1042ed74), X(0x105854c3), X(0x106dc879), X(0x10834892),
  X(0x1098d508), X(0x10ae6dd8), X(0x10c412fc), X(0x10d9c46f),
  X(0x10ef822d), X(0x11054c30), X(0x111b2274), X(0x113104f5),
  X(0x1146f3ac), X(0x115cee95), X(0x1172f5ab), X(0x118908e9),
  X(0x119f284a), X(0x11b553ca), X(0x11cb8b62), X(0x11e1cf0f),
  X(0x11f81ecb), X(0x120e7a90), X(0x1224e25a), X(0x123b5624),
  X(0x1251d5e9), X(0x126861a3), X(0x127ef94e), X(0x12959ce3),
  X(0x12ac4c5f), X(0x12c307bb), X(0x12d9cef2), X(0x12f0a200),
  X(0x130780df), X(0x131e6b8a), X(0x133561fa), X(0x134c642c),
  X(0x1363721a), X(0x137a8bbe), X(0x1391b113), X(0x13a8e214),
  X(0x13c01eba), X(0x13d76702), X(0x13eebae5), X(0x14061a5e),
  X(0x141d8567), X(0x1434fbfb), X(0x144c7e14), X(0x14640bae),
  X(0x147ba4c1), X(0x14934949), X(0x14aaf941), X(0x14c2b4a2),
  X(0x14da7b67), X(0x14f24d8a), X(0x150a2b06), X(0x152213d5),
  X(0x153a07f1), X(0x15520755), X(0x156a11fb), X(0x158227dd),
  X(0x159a48f5), X(0x15b2753d), X(0x15caacb1), X(0x15e2ef49),
  X(0x15fb3d01), X(0x161395d2), X(0x162bf9b6), X(0x164468a8),
  X(0x165ce2a1), X(0x1675679c), X(0x168df793), X(0x16a69280),
  X(0x16bf385c), X(0x16d7e922), X(0x16f0a4cc), X(0x17096b54),
  X(0x17223cb4), X(0x173b18e5), X(0x1753ffe2), X(0x176cf1a5),
  X(0x1785ee27), X(0x179ef562), X(0x17b80750), X(0x17d123eb),
  X(0x17ea4b2d), X(0x18037d10), X(0x181cb98d), X(0x1836009e),
  X(0x184f523c), X(0x1868ae63), X(0x1882150a), X(0x189b862c),
  X(0x18b501c4), X(0x18ce87c9), X(0x18e81836), X(0x1901b305),
  X(0x191b582f), X(0x193507ad), X(0x194ec17a), X(0x1968858f),
  X(0x198253e5), X(0x199c2c75), X(0x19b60f3a), X(0x19cffc2d),
  X(0x19e9f347), X(0x1a03f482), X(0x1a1dffd7), X(0x1a381540),
  X(0x1a5234b5), X(0x1a6c5e31), X(0x1a8691ac), X(0x1aa0cf21),
  X(0x1abb1687), X(0x1ad567da), X(0x1aefc311), X(0x1b0a2826),
  X(0x1b249712), X(0x1b3f0fd0), X(0x1b599257), X(0x1b741ea1),
  X(0x1b8eb4a7), X(0x1ba95462), X(0x1bc3fdcd), X(0x1bdeb0de),
  X(0x1bf96d91), X(0x1c1433dd), X(0x1c2f03bc), X(0x1c49dd27),
  X(0x1c64c017), X(0x1c7fac85), X(0x1c9aa269), X(0x1cb5a1be),
  X(0x1cd0aa7c), X(0x1cebbc9c), X(0x1d06d816), X(0x1d21fce4),
  X(0x1d3d2aff), X(0x1d586260), X(0x1d73a2fe), X(0x1d8eecd4),
  X(0x1daa3fda), X(0x1dc59c09), X(0x1de1015a), X(0x1dfc6fc5),
  X(0x1e17e743), X(0x1e3367cd), X(0x1e4ef15b), X(0x1e6a83e7),
  X(0x1e861f6a), X(0x1ea1c3da), X(0x1ebd7133), X(0x1ed9276b),
  X(0x1ef4e67c), X(0x1f10ae5e), X(0x1f2c7f0a), X(0x1f485879),
  X(0x1f643aa2), X(0x1f80257f), X(0x1f9c1908), X(0x1fb81536),
  X(0x1fd41a00), X(0x1ff02761), X(0x200c3d4f), X(0x20285bc3),
  X(0x204482b7), X(0x2060b221), X(0x207ce9fb), X(0x20992a3e),
  X(0x20b572e0), X(0x20d1c3dc), X(0x20ee1d28), X(0x210a7ebe),
  X(0x2126e895), X(0x21435aa6), X(0x215fd4ea), X(0x217c5757),
  X(0x2198e1e8), X(0x21b57493), X(0x21d20f51), X(0x21eeb21b),
  X(0x220b5ce7), X(0x22280fb0), X(0x2244ca6c), X(0x22618d13),
  X(0x227e579f), X(0x229b2a06), X(0x22b80442), X(0x22d4e649),
  X(0x22f1d015), X(0x230ec19d), X(0x232bbad9), X(0x2348bbc1),
  X(0x2365c44c), X(0x2382d474), X(0x239fec30), X(0x23bd0b78),
  X(0x23da3244), X(0x23f7608b), X(0x24149646), X(0x2431d36c),
  X(0x244f17f5), X(0x246c63da), X(0x2489b711), X(0x24a71193),
  X(0x24c47358), X(0x24e1dc57), X(0x24ff4c88), X(0x251cc3e2),
  X(0x253a425e), X(0x2557c7f4), X(0x2575549a), X(0x2592e848),
  X(0x25b082f7), X(0x25ce249e), X(0x25ebcd34), X(0x26097cb2),
  X(0x2627330e), X(0x2644f040), X(0x2662b441), X(0x26807f07),
  X(0x269e5089), X(0x26bc28c1), X(0x26da07a4), X(0x26f7ed2b),
  X(0x2715d94d), X(0x2733cc02), X(0x2751c540), X(0x276fc500),
  X(0x278dcb39), X(0x27abd7e2), X(0x27c9eaf3), X(0x27e80463),
  X(0x28062429), X(0x28244a3e), X(0x28427697), X(0x2860a92d),
  X(0x287ee1f7), X(0x289d20eb), X(0x28bb6603), X(0x28d9b134),
  X(0x28f80275), X(0x291659c0), X(0x2934b709), X(0x29531a49),
  X(0x29718378), X(0x298ff28b), X(0x29ae677b), X(0x29cce23e),
  X(0x29eb62cb), X(0x2a09e91b), X(0x2a287523), X(0x2a4706dc),
  X(0x2a659e3c), X(0x2a843b39), X(0x2aa2ddcd), X(0x2ac185ec),
  X(0x2ae0338f), X(0x2afee6ad), X(0x2b1d9f3c), X(0x2b3c5d33),
  X(0x2b5b208b), X(0x2b79e939), X(0x2b98b734), X(0x2bb78a74),
  X(0x2bd662ef), X(0x2bf5409d), X(0x2c142374), X(0x2c330b6b),
  X(0x2c51f87a), X(0x2c70ea97), X(0x2c8fe1b9), X(0x2caeddd6),
  X(0x2ccddee7), X(0x2cece4e1), X(0x2d0befbb), X(0x2d2aff6d),
  X(0x2d4a13ec), X(0x2d692d31), X(0x2d884b32), X(0x2da76de4),
  X(0x2dc69540), X(0x2de5c13d), X(0x2e04f1d0), X(0x2e2426f0),
  X(0x2e436095), X(0x2e629eb4), X(0x2e81e146), X(0x2ea1283f),
  X(0x2ec07398), X(0x2edfc347), X(0x2eff1742), X(0x2f1e6f80),
  X(0x2f3dcbf8), X(0x2f5d2ca0), X(0x2f7c916f), X(0x2f9bfa5c),
  X(0x2fbb675d), X(0x2fdad869), X(0x2ffa4d76), X(0x3019c67b),
  X(0x3039436f), X(0x3058c448), X(0x307848fc), X(0x3097d183),
  X(0x30b75dd3), X(0x30d6ede2), X(0x30f681a6), X(0x31161917),
  X(0x3135b42b), X(0x315552d8), X(0x3174f514), X(0x31949ad7),
  X(0x31b44417), X(0x31d3f0ca), X(0x31f3a0e6), X(0x32135462),
  X(0x32330b35), X(0x3252c555), X(0x327282b7), X(0x32924354),
  X(0x32b20720), X(0x32d1ce13), X(0x32f19823), X(0x33116546),
  X(0x33313573), X(0x3351089f), X(0x3370dec2), X(0x3390b7d1),
  X(0x33b093c3), X(0x33d0728f), X(0x33f05429), X(0x3410388a),
  X(0x34301fa7), X(0x34500977), X(0x346ff5ef), X(0x348fe506),
  X(0x34afd6b3), X(0x34cfcaeb), X(0x34efc1a5), X(0x350fbad7),
  X(0x352fb678), X(0x354fb47d), X(0x356fb4dd), X(0x358fb78e),
  X(0x35afbc86), X(0x35cfc3bc), X(0x35efcd25), X(0x360fd8b8),
  X(0x362fe66c), X(0x364ff636), X(0x3670080c), X(0x36901be5),
  X(0x36b031b7), X(0x36d04978), X(0x36f0631e), X(0x37107ea0),
  X(0x37309bf3), X(0x3750bb0e), X(0x3770dbe6), X(0x3790fe73),
  X(0x37b122aa), X(0x37d14881), X(0x37f16fee), X(0x381198e8),
  X(0x3831c365), X(0x3851ef5a), X(0x38721cbe), X(0x38924b87),
  X(0x38b27bac), X(0x38d2ad21), X(0x38f2dfde), X(0x391313d8),
  X(0x39334906), X(0x39537f5d), X(0x3973b6d4), X(0x3993ef60),
  X(0x39b428f9), X(0x39d46393), X(0x39f49f25), X(0x3a14dba6),
  X(0x3a35190a), X(0x3a555748), X(0x3a759657), X(0x3a95d62c),
  X(0x3ab616be), X(0x3ad65801), X(0x3af699ed), X(0x3b16dc78),
  X(0x3b371f97), X(0x3b576341), X(0x3b77a76c), X(0x3b97ec0d),
  X(0x3bb8311b), X(0x3bd8768b), X(0x3bf8bc55), X(0x3c19026d),
  X(0x3c3948ca), X(0x3c598f62), X(0x3c79d62b), X(0x3c9a1d1b),
  X(0x3cba6428), X(0x3cdaab48), X(0x3cfaf271), X(0x3d1b3999),
  X(0x3d3b80b6), X(0x3d5bc7be), X(0x3d7c0ea8), X(0x3d9c5569),
  X(0x3dbc9bf7), X(0x3ddce248), X(0x3dfd2852), X(0x3e1d6e0c),
  X(0x3e3db36c), X(0x3e5df866), X(0x3e7e3cf2), X(0x3e9e8106),
  X(0x3ebec497), X(0x3edf079b), X(0x3eff4a09), X(0x3f1f8bd7),
  X(0x3f3fccfa), X(0x3f600d69), X(0x3f804d1a), X(0x3fa08c02),
  X(0x3fc0ca19), X(0x3fe10753), X(0x400143a7), X(0x40217f0a),
  X(0x4041b974), X(0x4061f2da), X(0x40822b32), X(0x40a26272),
  X(0x40c29891), X(0x40e2cd83), X(0x41030140), X(0x412333bd),
  X(0x414364f1), X(0x416394d2), X(0x4183c355), X(0x41a3f070),
  X(0x41c41c1b), X(0x41e4464a), X(0x42046ef4), X(0x42249610),
  X(0x4244bb92), X(0x4264df72), X(0x428501a5), X(0x42a52222),
  X(0x42c540de), X(0x42e55dd0), X(0x430578ed), X(0x4325922d),
  X(0x4345a985), X(0x4365beeb), X(0x4385d255), X(0x43a5e3ba),
  X(0x43c5f30f), X(0x43e6004b), X(0x44060b65), X(0x44261451),
  X(0x44461b07), X(0x44661f7c), X(0x448621a7), X(0x44a6217d),
  X(0x44c61ef6), X(0x44e61a07), X(0x450612a6), X(0x452608ca),
  X(0x4545fc69), X(0x4565ed79), X(0x4585dbf1), X(0x45a5c7c6),
  X(0x45c5b0ef), X(0x45e59761), X(0x46057b15), X(0x46255bfe),
  X(0x46453a15), X(0x4665154f), X(0x4684eda2), X(0x46a4c305),
  X(0x46c4956e), X(0x46e464d3), X(0x4704312b), X(0x4723fa6c),
  X(0x4743c08d), X(0x47638382), X(0x47834344), X(0x47a2ffc9),
  X(0x47c2b906), X(0x47e26ef2), X(0x48022183), X(0x4821d0b1),
  X(0x48417c71), X(0x486124b9), X(0x4880c981), X(0x48a06abe),
  X(0x48c00867), X(0x48dfa272), X(0x48ff38d6), X(0x491ecb8a),
  X(0x493e5a84), X(0x495de5b9), X(0x497d6d22), X(0x499cf0b4),
  X(0x49bc7066), X(0x49dbec2e), X(0x49fb6402), X(0x4a1ad7db),
  X(0x4a3a47ad), X(0x4a59b370), X(0x4a791b1a), X(0x4a987ea1),
  X(0x4ab7ddfd), X(0x4ad73924), X(0x4af6900c), X(0x4b15e2ad),
  X(0x4b3530fc), X(0x4b547af1), X(0x4b73c082), X(0x4b9301a6),
  X(0x4bb23e53), X(0x4bd17681), X(0x4bf0aa25), X(0x4c0fd937),
  X(0x4c2f03ae), X(0x4c4e297f), X(0x4c6d4aa3), X(0x4c8c670f),
  X(0x4cab7eba), X(0x4cca919c), X(0x4ce99fab), X(0x4d08a8de),
  X(0x4d27ad2c), X(0x4d46ac8b), X(0x4d65a6f3), X(0x4d849c5a),
  X(0x4da38cb7), X(0x4dc27802), X(0x4de15e31), X(0x4e003f3a),
  X(0x4e1f1b16), X(0x4e3df1ba), X(0x4e5cc31e), X(0x4e7b8f3a),
  X(0x4e9a5603), X(0x4eb91771), X(0x4ed7d37b), X(0x4ef68a18),
  X(0x4f153b3f), X(0x4f33e6e7), X(0x4f528d08), X(0x4f712d97),
  X(0x4f8fc88e), X(0x4fae5de1), X(0x4fcced8a), X(0x4feb777f),
  X(0x5009fbb6), X(0x50287a28), X(0x5046f2cc), X(0x50656598),
  X(0x5083d284), X(0x50a23988), X(0x50c09a9a), X(0x50def5b1),
  X(0x50fd4ac7), X(0x511b99d0), X(0x5139e2c5), X(0x5158259e),
  X(0x51766251), X(0x519498d6), X(0x51b2c925), X(0x51d0f334),
  X(0x51ef16fb), X(0x520d3473), X(0x522b4b91), X(0x52495c4e),
  X(0x526766a2), X(0x52856a83), X(0x52a367e9), X(0x52c15ecd),
  X(0x52df4f24), X(0x52fd38e8), X(0x531b1c10), X(0x5338f892),
  X(0x5356ce68), X(0x53749d89), X(0x539265eb), X(0x53b02788),
  X(0x53cde257), X(0x53eb964f), X(0x54094369), X(0x5426e99c),
  X(0x544488df), X(0x5462212c), X(0x547fb279), X(0x549d3cbe),
  X(0x54babff4), X(0x54d83c12), X(0x54f5b110), X(0x55131ee7),
  X(0x5530858d), X(0x554de4fc), X(0x556b3d2a), X(0x55888e11),
  X(0x55a5d7a8), X(0x55c319e7), X(0x55e054c7), X(0x55fd883f),
  X(0x561ab447), X(0x5637d8d8), X(0x5654f5ea), X(0x56720b75),
  X(0x568f1971), X(0x56ac1fd7), X(0x56c91e9e), X(0x56e615c0),
  X(0x57030534), X(0x571fecf2), X(0x573cccf3), X(0x5759a530),
  X(0x577675a0), X(0x57933e3c), X(0x57affefd), X(0x57ccb7db),
  X(0x57e968ce), X(0x580611cf), X(0x5822b2d6), X(0x583f4bdd),
  X(0x585bdcdb), X(0x587865c9), X(0x5894e69f), X(0x58b15f57),
  X(0x58cdcfe9), X(0x58ea384e), X(0x5906987d), X(0x5922f071),
  X(0x593f4022), X(0x595b8788), X(0x5977c69c), X(0x5993fd57),
  X(0x59b02bb2), X(0x59cc51a6), X(0x59e86f2c), X(0x5a04843c),
  X(0x5a2090d0), X(0x5a3c94e0), X(0x5a589065), X(0x5a748359),
  X(0x5a906db4), X(0x5aac4f70), X(0x5ac82884), X(0x5ae3f8ec),
  X(0x5affc09f), X(0x5b1b7f97), X(0x5b3735cd), X(0x5b52e33a),
  X(0x5b6e87d8), X(0x5b8a239f), X(0x5ba5b689), X(0x5bc1408f),
  X(0x5bdcc1aa), X(0x5bf839d5), X(0x5c13a907), X(0x5c2f0f3b),
  X(0x5c4a6c6a), X(0x5c65c08d), X(0x5c810b9e), X(0x5c9c4d97),
  X(0x5cb78670), X(0x5cd2b623), X(0x5ceddcaa), X(0x5d08f9ff),
  X(0x5d240e1b), X(0x5d3f18f8), X(0x5d5a1a8f), X(0x5d7512da),
  X(0x5d9001d3), X(0x5daae773), X(0x5dc5c3b5), X(0x5de09692),
  X(0x5dfb6004), X(0x5e162004), X(0x5e30d68d), X(0x5e4b8399),
  X(0x5e662721), X(0x5e80c11f), X(0x5e9b518e), X(0x5eb5d867),
  X(0x5ed055a4), X(0x5eeac940), X(0x5f053334), X(0x5f1f937b),
  X(0x5f39ea0f), X(0x5f5436ea), X(0x5f6e7a06), X(0x5f88b35d),
  X(0x5fa2e2e9), X(0x5fbd08a6), X(0x5fd7248d), X(0x5ff13698),
  X(0x600b3ec2), X(0x60253d05), X(0x603f315b), X(0x60591bc0),
  X(0x6072fc2d), X(0x608cd29e), X(0x60a69f0b), X(0x60c06171),
  X(0x60da19ca), X(0x60f3c80f), X(0x610d6c3d), X(0x6127064d),
  X(0x6140963a), X(0x615a1bff), X(0x61739797), X(0x618d08fc),
  X(0x61a67029), X(0x61bfcd1a), X(0x61d91fc8), X(0x61f2682f),
  X(0x620ba64a), X(0x6224da13), X(0x623e0386), X(0x6257229d),
  X(0x62703754), X(0x628941a6), X(0x62a2418e), X(0x62bb3706),
  X(0x62d4220a), X(0x62ed0296), X(0x6305d8a3), X(0x631ea42f),
  X(0x63376533), X(0x63501bab), X(0x6368c793), X(0x638168e5),
  X(0x6399ff9e), X(0x63b28bb8), X(0x63cb0d2f), X(0x63e383ff),
  X(0x63fbf022), X(0x64145195), X(0x642ca853), X(0x6444f457),
  X(0x645d359e), X(0x64756c22), X(0x648d97e0), X(0x64a5b8d3),
  X(0x64bdcef6), X(0x64d5da47), X(0x64eddabf), X(0x6505d05c),
  X(0x651dbb19), X(0x65359af2), X(0x654d6fe3), X(0x656539e7),
  X(0x657cf8fb), X(0x6594ad1b), X(0x65ac5643), X(0x65c3f46e),
  X(0x65db8799), X(0x65f30fc0), X(0x660a8ce0), X(0x6621fef3),
  X(0x663965f7), X(0x6650c1e7), X(0x666812c1), X(0x667f5880),
  X(0x66969320), X(0x66adc29e), X(0x66c4e6f7), X(0x66dc0026),
  X(0x66f30e28), X(0x670a10fa), X(0x67210898), X(0x6737f4ff),
  X(0x674ed62b), X(0x6765ac19), X(0x677c76c5), X(0x6793362c),
  X(0x67a9ea4b), X(0x67c0931f), X(0x67d730a3), X(0x67edc2d6),
  X(0x680449b3), X(0x681ac538), X(0x68313562), X(0x68479a2d),
  X(0x685df396), X(0x6874419b), X(0x688a8438), X(0x68a0bb6a),
  X(0x68b6e72e), X(0x68cd0782), X(0x68e31c63), X(0x68f925cd),
  X(0x690f23be), X(0x69251633), X(0x693afd29), X(0x6950d89e),
  X(0x6966a88f), X(0x697c6cf8), X(0x699225d9), X(0x69a7d32d),
  X(0x69bd74f3), X(0x69d30b27), X(0x69e895c8), X(0x69fe14d2),
  X(0x6a138844), X(0x6a28f01b), X(0x6a3e4c54), X(0x6a539ced),
  X(0x6a68e1e4), X(0x6a7e1b37), X(0x6a9348e3), X(0x6aa86ae6),
  X(0x6abd813d), X(0x6ad28be7), X(0x6ae78ae2), X(0x6afc7e2b),
  X(0x6b1165c0), X(0x6b26419f), X(0x6b3b11c7), X(0x6b4fd634),
  X(0x6b648ee6), X(0x6b793bda), X(0x6b8ddd0e), X(0x6ba27281),
  X(0x6bb6fc31), X(0x6bcb7a1b), X(0x6bdfec3e), X(0x6bf45299),
  X(0x6c08ad29), X(0x6c1cfbed), X(0x6c313ee4), X(0x6c45760a),
  X(0x6c59a160), X(0x6c6dc0e4), X(0x6c81d493), X(0x6c95dc6d),
  X(0x6ca9d86f), X(0x6cbdc899), X(0x6cd1acea), X(0x6ce5855f),
  X(0x6cf951f7), X(0x6d0d12b1), X(0x6d20c78c), X(0x6d347087),
  X(0x6d480da0), X(0x6d5b9ed6), X(0x6d6f2427), X(0x6d829d94),
  X(0x6d960b1a), X(0x6da96cb9), X(0x6dbcc270), X(0x6dd00c3c),
  X(0x6de34a1f), X(0x6df67c16), X(0x6e09a221), X(0x6e1cbc3f),
  X(0x6e2fca6e), X(0x6e42ccaf), X(0x6e55c300), X(0x6e68ad60),
  X(0x6e7b8bd0), X(0x6e8e5e4d), X(0x6ea124d8), X(0x6eb3df70),
  X(0x6ec68e13), X(0x6ed930c3), X(0x6eebc77d), X(0x6efe5242),
  X(0x6f10d111), X(0x6f2343e9), X(0x6f35aacb), X(0x6f4805b5),
  X(0x6f5a54a8), X(0x6f6c97a2), X(0x6f7ecea4), X(0x6f90f9ae),
  X(0x6fa318be), X(0x6fb52bd6), X(0x6fc732f4), X(0x6fd92e19),
  X(0x6feb1d44), X(0x6ffd0076), X(0x700ed7ad), X(0x7020a2eb),
  X(0x7032622f), X(0x7044157a), X(0x7055bcca), X(0x70675821),
  X(0x7078e77e), X(0x708a6ae2), X(0x709be24c), X(0x70ad4dbd),
  X(0x70bead36), X(0x70d000b5), X(0x70e1483d), X(0x70f283cc),
  X(0x7103b363), X(0x7114d704), X(0x7125eead), X(0x7136fa60),
  X(0x7147fa1c), X(0x7158ede4), X(0x7169d5b6), X(0x717ab193),
  X(0x718b817d), X(0x719c4573), X(0x71acfd76), X(0x71bda988),
  X(0x71ce49a8), X(0x71deddd7), X(0x71ef6617), X(0x71ffe267),
  X(0x721052ca), X(0x7220b73e), X(0x72310fc6), X(0x72415c62),
  X(0x72519d14), X(0x7261d1db), X(0x7271faba), X(0x728217b1),
  X(0x729228c0), X(0x72a22dea), X(0x72b22730), X(0x72c21491),
  X(0x72d1f611), X(0x72e1cbaf), X(0x72f1956c), X(0x7301534c),
  X(0x7311054d), X(0x7320ab72), X(0x733045bc), X(0x733fd42d),
  X(0x734f56c5), X(0x735ecd86), X(0x736e3872), X(0x737d9789),
  X(0x738ceacf), X(0x739c3243), X(0x73ab6de7), X(0x73ba9dbe),
  X(0x73c9c1c8), X(0x73d8da08), X(0x73e7e67f), X(0x73f6e72e),
  X(0x7405dc17), X(0x7414c53c), X(0x7423a29f), X(0x74327442),
  X(0x74413a26), X(0x744ff44d), X(0x745ea2b9), X(0x746d456c),
  X(0x747bdc68), X(0x748a67ae), X(0x7498e741), X(0x74a75b23),
  X(0x74b5c356), X(0x74c41fdb), X(0x74d270b6), X(0x74e0b5e7),
  X(0x74eeef71), X(0x74fd1d57), X(0x750b3f9a), X(0x7519563c),
  X(0x75276140), X(0x753560a8), X(0x75435477), X(0x75513cae),
  X(0x755f1951), X(0x756cea60), X(0x757aafdf), X(0x758869d1),
  X(0x75961837), X(0x75a3bb14), X(0x75b1526a), X(0x75bede3c),
  X(0x75cc5e8d), X(0x75d9d35f), X(0x75e73cb5), X(0x75f49a91),
  X(0x7601ecf6), X(0x760f33e6), X(0x761c6f65), X(0x76299f74),
  X(0x7636c417), X(0x7643dd51), X(0x7650eb24), X(0x765ded93),
  X(0x766ae4a0), X(0x7677d050), X(0x7684b0a4), X(0x7691859f),
  X(0x769e4f45), X(0x76ab0d98), X(0x76b7c09c), X(0x76c46852),
  X(0x76d104bf), X(0x76dd95e6), X(0x76ea1bc9), X(0x76f6966b),
  X(0x770305d0), X(0x770f69fb), X(0x771bc2ef), X(0x772810af),
  X(0x7734533e), X(0x77408aa0), X(0x774cb6d7), X(0x7758d7e8),
  X(0x7764edd5), X(0x7770f8a2), X(0x777cf852), X(0x7788ece8),
  X(0x7794d668), X(0x77a0b4d5), X(0x77ac8833), X(0x77b85085),
  X(0x77c40dce), X(0x77cfc013), X(0x77db6756), X(0x77e7039b),
  X(0x77f294e6), X(0x77fe1b3b), X(0x7809969c), X(0x7815070e),
  X(0x78206c93), X(0x782bc731), X(0x783716ea), X(0x78425bc3),
  X(0x784d95be), X(0x7858c4e1), X(0x7863e92d), X(0x786f02a8),
  X(0x787a1156), X(0x78851539), X(0x78900e56), X(0x789afcb1),
  X(0x78a5e04d), X(0x78b0b92f), X(0x78bb875b), X(0x78c64ad4),
  X(0x78d1039e), X(0x78dbb1be), X(0x78e65537), X(0x78f0ee0e),
  X(0x78fb7c46), X(0x7905ffe4), X(0x791078ec), X(0x791ae762),
  X(0x79254b4a), X(0x792fa4a7), X(0x7939f380), X(0x794437d7),
  X(0x794e71b0), X(0x7958a111), X(0x7962c5fd), X(0x796ce078),
  X(0x7976f087), X(0x7980f62f), X(0x798af173), X(0x7994e258),
  X(0x799ec8e2), X(0x79a8a515), X(0x79b276f7), X(0x79bc3e8b),
  X(0x79c5fbd6), X(0x79cfaedc), X(0x79d957a2), X(0x79e2f62c),
  X(0x79ec8a7f), X(0x79f6149f), X(0x79ff9492), X(0x7a090a5a),
  X(0x7a1275fe), X(0x7a1bd781), X(0x7a252ee9), X(0x7a2e7c39),
  X(0x7a37bf77), X(0x7a40f8a7), X(0x7a4a27ce), X(0x7a534cf0),
  X(0x7a5c6813), X(0x7a65793b), X(0x7a6e806d), X(0x7a777dad),
  X(0x7a807100), X(0x7a895a6b), X(0x7a9239f4), X(0x7a9b0f9e),
  X(0x7aa3db6f), X(0x7aac9d6b), X(0x7ab55597), X(0x7abe03f9),
  X(0x7ac6a895), X(0x7acf4370), X(0x7ad7d48f), X(0x7ae05bf6),
  X(0x7ae8d9ac), X(0x7af14db5), X(0x7af9b815), X(0x7b0218d2),
  X(0x7b0a6ff2), X(0x7b12bd78), X(0x7b1b016a), X(0x7b233bce),
  X(0x7b2b6ca7), X(0x7b3393fc), X(0x7b3bb1d1), X(0x7b43c62c),
  X(0x7b4bd111), X(0x7b53d286), X(0x7b5bca90), X(0x7b63b935),
  X(0x7b6b9e78), X(0x7b737a61), X(0x7b7b4cf3), X(0x7b831634),
  X(0x7b8ad629), X(0x7b928cd8), X(0x7b9a3a45), X(0x7ba1de77),
  X(0x7ba97972), X(0x7bb10b3c), X(0x7bb893d9), X(0x7bc01350),
  X(0x7bc789a6), X(0x7bcef6e0), X(0x7bd65b03), X(0x7bddb616),
  X(0x7be5081c), X(0x7bec511c), X(0x7bf3911b), X(0x7bfac81f),
  X(0x7c01f62c), X(0x7c091b49), X(0x7c10377b), X(0x7c174ac7),
  X(0x7c1e5532), X(0x7c2556c4), X(0x7c2c4f80), X(0x7c333f6c),
  X(0x7c3a268e), X(0x7c4104ec), X(0x7c47da8a), X(0x7c4ea76f),
  X(0x7c556ba1), X(0x7c5c2724), X(0x7c62d9fe), X(0x7c698435),
  X(0x7c7025cf), X(0x7c76bed0), X(0x7c7d4f40), X(0x7c83d723),
  X(0x7c8a567f), X(0x7c90cd5a), X(0x7c973bb9), X(0x7c9da1a2),
  X(0x7ca3ff1b), X(0x7caa542a), X(0x7cb0a0d3), X(0x7cb6e51e),
  X(0x7cbd210f), X(0x7cc354ac), X(0x7cc97ffc), X(0x7ccfa304),
  X(0x7cd5bdc9), X(0x7cdbd051), X(0x7ce1daa3), X(0x7ce7dcc3),
  X(0x7cedd6b8), X(0x7cf3c888), X(0x7cf9b238), X(0x7cff93cf),
  X(0x7d056d51), X(0x7d0b3ec5), X(0x7d110830), X(0x7d16c99a),
  X(0x7d1c8306), X(0x7d22347c), X(0x7d27de00), X(0x7d2d7f9a),
  X(0x7d33194f), X(0x7d38ab24), X(0x7d3e351f), X(0x7d43b748),
  X(0x7d4931a2), X(0x7d4ea435), X(0x7d540f06), X(0x7d59721b),
  X(0x7d5ecd7b), X(0x7d64212a), X(0x7d696d2f), X(0x7d6eb190),
  X(0x7d73ee53), X(0x7d79237e), X(0x7d7e5117), X(0x7d837723),
  X(0x7d8895a9), X(0x7d8dacae), X(0x7d92bc3a), X(0x7d97c451),
  X(0x7d9cc4f9), X(0x7da1be39), X(0x7da6b017), X(0x7dab9a99),
  X(0x7db07dc4), X(0x7db5599e), X(0x7dba2e2f), X(0x7dbefb7b),
  X(0x7dc3c189), X(0x7dc8805e), X(0x7dcd3802), X(0x7dd1e879),
  X(0x7dd691ca), X(0x7ddb33fb), X(0x7ddfcf12), X(0x7de46315),
  X(0x7de8f00a), X(0x7ded75f8), X(0x7df1f4e3), X(0x7df66cd3),
  X(0x7dfaddcd), X(0x7dff47d7), X(0x7e03aaf8), X(0x7e080735),
  X(0x7e0c5c95), X(0x7e10ab1e), X(0x7e14f2d5), X(0x7e1933c1),
  X(0x7e1d6de8), X(0x7e21a150), X(0x7e25cdff), X(0x7e29f3fc),
  X(0x7e2e134c), X(0x7e322bf5), X(0x7e363dfd), X(0x7e3a496b),
  X(0x7e3e4e45), X(0x7e424c90), X(0x7e464454), X(0x7e4a3595),
  X(0x7e4e205a), X(0x7e5204aa), X(0x7e55e289), X(0x7e59b9ff),
  X(0x7e5d8b12), X(0x7e6155c7), X(0x7e651a24), X(0x7e68d831),
  X(0x7e6c8ff2), X(0x7e70416e), X(0x7e73ecac), X(0x7e7791b0),
  X(0x7e7b3082), X(0x7e7ec927), X(0x7e825ba6), X(0x7e85e804),
  X(0x7e896e48), X(0x7e8cee77), X(0x7e906899), X(0x7e93dcb2),
  X(0x7e974aca), X(0x7e9ab2e5), X(0x7e9e150b), X(0x7ea17141),
  X(0x7ea4c78e), X(0x7ea817f7), X(0x7eab6283), X(0x7eaea737),
  X(0x7eb1e61a), X(0x7eb51f33), X(0x7eb85285), X(0x7ebb8019),
  X(0x7ebea7f4), X(0x7ec1ca1d), X(0x7ec4e698), X(0x7ec7fd6d),
  X(0x7ecb0ea1), X(0x7ece1a3a), X(0x7ed1203f), X(0x7ed420b6),
  X(0x7ed71ba4), X(0x7eda110f), X(0x7edd00ff), X(0x7edfeb78),
  X(0x7ee2d081), X(0x7ee5b01f), X(0x7ee88a5a), X(0x7eeb5f36),
  X(0x7eee2eba), X(0x7ef0f8ed), X(0x7ef3bdd3), X(0x7ef67d73),
  X(0x7ef937d3), X(0x7efbecf9), X(0x7efe9ceb), X(0x7f0147ae),
  X(0x7f03ed4a), X(0x7f068dc4), X(0x7f092922), X(0x7f0bbf69),
  X(0x7f0e50a1), X(0x7f10dcce), X(0x7f1363f7), X(0x7f15e622),
  X(0x7f186355), X(0x7f1adb95), X(0x7f1d4ee9), X(0x7f1fbd57),
  X(0x7f2226e4), X(0x7f248b96), X(0x7f26eb74), X(0x7f294683),
  X(0x7f2b9cc9), X(0x7f2dee4d), X(0x7f303b13), X(0x7f328322),
  X(0x7f34c680), X(0x7f370533), X(0x7f393f40), X(0x7f3b74ad),
  X(0x7f3da581), X(0x7f3fd1c1), X(0x7f41f972), X(0x7f441c9c),
  X(0x7f463b43), X(0x7f48556d), X(0x7f4a6b21), X(0x7f4c7c64),
  X(0x7f4e893c), X(0x7f5091ae), X(0x7f5295c1), X(0x7f54957a),
  X(0x7f5690e0), X(0x7f5887f7), X(0x7f5a7ac5), X(0x7f5c6951),
  X(0x7f5e53a0), X(0x7f6039b8), X(0x7f621b9e), X(0x7f63f958),
  X(0x7f65d2ed), X(0x7f67a861), X(0x7f6979ba), X(0x7f6b46ff),
  X(0x7f6d1034), X(0x7f6ed560), X(0x7f709687), X(0x7f7253b1),
  X(0x7f740ce1), X(0x7f75c21f), X(0x7f777370), X(0x7f7920d8),
  X(0x7f7aca5f), X(0x7f7c7008), X(0x7f7e11db), X(0x7f7fafdd),
  X(0x7f814a13), X(0x7f82e082), X(0x7f847331), X(0x7f860224),
  X(0x7f878d62), X(0x7f8914f0), X(0x7f8a98d4), X(0x7f8c1912),
  X(0x7f8d95b0), X(0x7f8f0eb5), X(0x7f908425), X(0x7f91f605),
  X(0x7f93645c), X(0x7f94cf2f), X(0x7f963683), X(0x7f979a5d),
  X(0x7f98fac4), X(0x7f9a57bb), X(0x7f9bb14a), X(0x7f9d0775),
  X(0x7f9e5a41), X(0x7f9fa9b4), X(0x7fa0f5d3), X(0x7fa23ea4),
  X(0x7fa3842b), X(0x7fa4c66f), X(0x7fa60575), X(0x7fa74141),
  X(0x7fa879d9), X(0x7fa9af42), X(0x7faae182), X(0x7fac109e),
  X(0x7fad3c9a), X(0x7fae657d), X(0x7faf8b4c), X(0x7fb0ae0b),
  X(0x7fb1cdc0), X(0x7fb2ea70), X(0x7fb40420), X(0x7fb51ad5),
  X(0x7fb62e95), X(0x7fb73f64), X(0x7fb84d48), X(0x7fb95846),
  X(0x7fba6062), X(0x7fbb65a2), X(0x7fbc680c), X(0x7fbd67a3),
  X(0x7fbe646d), X(0x7fbf5e70), X(0x7fc055af), X(0x7fc14a31),
  X(0x7fc23bf9), X(0x7fc32b0d), X(0x7fc41773), X(0x7fc5012e),
  X(0x7fc5e844), X(0x7fc6ccba), X(0x7fc7ae94), X(0x7fc88dd8),
  X(0x7fc96a8a), X(0x7fca44af), X(0x7fcb1c4c), X(0x7fcbf167),
  X(0x7fccc403), X(0x7fcd9425), X(0x7fce61d3), X(0x7fcf2d11),
  X(0x7fcff5e3), X(0x7fd0bc4f), X(0x7fd1805a), X(0x7fd24207),
  X(0x7fd3015c), X(0x7fd3be5d), X(0x7fd47910), X(0x7fd53178),
  X(0x7fd5e79b), X(0x7fd69b7c), X(0x7fd74d21), X(0x7fd7fc8e),
  X(0x7fd8a9c8), X(0x7fd954d4), X(0x7fd9fdb5), X(0x7fdaa471),
  X(0x7fdb490b), X(0x7fdbeb89), X(0x7fdc8bef), X(0x7fdd2a42),
  X(0x7fddc685), X(0x7fde60be), X(0x7fdef8f0), X(0x7fdf8f20),
  X(0x7fe02353), X(0x7fe0b58d), X(0x7fe145d3), X(0x7fe1d428),
  X(0x7fe26091), X(0x7fe2eb12), X(0x7fe373b0), X(0x7fe3fa6f),
  X(0x7fe47f53), X(0x7fe50260), X(0x7fe5839b), X(0x7fe60308),
  X(0x7fe680ab), X(0x7fe6fc88), X(0x7fe776a4), X(0x7fe7ef02),
  X(0x7fe865a7), X(0x7fe8da97), X(0x7fe94dd6), X(0x7fe9bf68),
  X(0x7fea2f51), X(0x7fea9d95), X(0x7feb0a39), X(0x7feb7540),
  X(0x7febdeae), X(0x7fec4687), X(0x7fecaccf), X(0x7fed118b),
  X(0x7fed74be), X(0x7fedd66c), X(0x7fee3698), X(0x7fee9548),
  X(0x7feef27e), X(0x7fef4e3f), X(0x7fefa88e), X(0x7ff0016f),
  X(0x7ff058e7), X(0x7ff0aef8), X(0x7ff103a6), X(0x7ff156f6),
  X(0x7ff1a8eb), X(0x7ff1f988), X(0x7ff248d2), X(0x7ff296cc),
  X(0x7ff2e37a), X(0x7ff32edf), X(0x7ff378ff), X(0x7ff3c1de),
  X(0x7ff4097e), X(0x7ff44fe5), X(0x7ff49515), X(0x7ff4d911),
  X(0x7ff51bde), X(0x7ff55d7f), X(0x7ff59df7), X(0x7ff5dd4a),
  X(0x7ff61b7b), X(0x7ff6588d), X(0x7ff69485), X(0x7ff6cf65),
  X(0x7ff70930), X(0x7ff741eb), X(0x7ff77998), X(0x7ff7b03b),
  X(0x7ff7e5d7), X(0x7ff81a6f), X(0x7ff84e06), X(0x7ff880a1),
  X(0x7ff8b241), X(0x7ff8e2ea), X(0x7ff912a0), X(0x7ff94165),
  X(0x7ff96f3d), X(0x7ff99c2b), X(0x7ff9c831), X(0x7ff9f354),
  X(0x7ffa1d95), X(0x7ffa46f9), X(0x7ffa6f81), X(0x7ffa9731),
  X(0x7ffabe0d), X(0x7ffae416), X(0x7ffb0951), X(0x7ffb2dbf),
  X(0x7ffb5164), X(0x7ffb7442), X(0x7ffb965d), X(0x7ffbb7b8),
  X(0x7ffbd854), X(0x7ffbf836), X(0x7ffc175f), X(0x7ffc35d3),
  X(0x7ffc5394), X(0x7ffc70a5), X(0x7ffc8d09), X(0x7ffca8c2),
  X(0x7ffcc3d4), X(0x7ffcde3f), X(0x7ffcf809), X(0x7ffd1132),
  X(0x7ffd29be), X(0x7ffd41ae), X(0x7ffd5907), X(0x7ffd6fc9),
  X(0x7ffd85f9), X(0x7ffd9b97), X(0x7ffdb0a7), X(0x7ffdc52b),
  X(0x7ffdd926), X(0x7ffdec99), X(0x7ffdff88), X(0x7ffe11f4),
  X(0x7ffe23e0), X(0x7ffe354f), X(0x7ffe4642), X(0x7ffe56bc),
  X(0x7ffe66bf), X(0x7ffe764e), X(0x7ffe856a), X(0x7ffe9416),
  X(0x7ffea254), X(0x7ffeb026), X(0x7ffebd8e), X(0x7ffeca8f),
  X(0x7ffed72a), X(0x7ffee362), X(0x7ffeef38), X(0x7ffefaaf),
  X(0x7fff05c9), X(0x7fff1087), X(0x7fff1aec), X(0x7fff24f9),
  X(0x7fff2eb1), X(0x7fff3816), X(0x7fff4128), X(0x7fff49eb),
  X(0x7fff5260), X(0x7fff5a88), X(0x7fff6266), X(0x7fff69fc),
  X(0x7fff714b), X(0x7fff7854), X(0x7fff7f1a), X(0x7fff859f),
  X(0x7fff8be3), X(0x7fff91ea), X(0x7fff97b3), X(0x7fff9d41),
  X(0x7fffa296), X(0x7fffa7b3), X(0x7fffac99), X(0x7fffb14b),
  X(0x7fffb5c9), X(0x7fffba15), X(0x7fffbe31), X(0x7fffc21d),
  X(0x7fffc5dc), X(0x7fffc96f), X(0x7fffccd8), X(0x7fffd016),
  X(0x7fffd32d), X(0x7fffd61c), X(0x7fffd8e7), X(0x7fffdb8d),
  X(0x7fffde0f), X(0x7fffe071), X(0x7fffe2b1), X(0x7fffe4d2),
  X(0x7fffe6d5), X(0x7fffe8bb), X(0x7fffea85), X(0x7fffec34),
  X(0x7fffedc9), X(0x7fffef45), X(0x7ffff0aa), X(0x7ffff1f7),
  X(0x7ffff330), X(0x7ffff453), X(0x7ffff562), X(0x7ffff65f),
  X(0x7ffff749), X(0x7ffff823), X(0x7ffff8ec), X(0x7ffff9a6),
  X(0x7ffffa51), X(0x7ffffaee), X(0x7ffffb7e), X(0x7ffffc02),
  X(0x7ffffc7a), X(0x7ffffce7), X(0x7ffffd4a), X(0x7ffffda3),
  X(0x7ffffdf4), X(0x7ffffe3c), X(0x7ffffe7c), X(0x7ffffeb6),
  X(0x7ffffee8), X(0x7fffff15), X(0x7fffff3c), X(0x7fffff5e),
  X(0x7fffff7b), X(0x7fffff95), X(0x7fffffaa), X(0x7fffffbc),
  X(0x7fffffcb), X(0x7fffffd7), X(0x7fffffe2), X(0x7fffffea),
  X(0x7ffffff0), X(0x7ffffff5), X(0x7ffffff9), X(0x7ffffffb),
  X(0x7ffffffd), X(0x7ffffffe), X(0x7fffffff), X(0x7fffffff),
  X(0x7fffffff), X(0x7fffffff), X(0x7fffffff), X(0x7fffffff),
};

#ifndef LIMIT_TO_64kHz

static LOOKUP_T vwin8192[4096] = {
  X(0x0000007c), X(0x0000045c), X(0x00000c1d), X(0x000017bd),
  X(0x0000273e), X(0x00003a9f), X(0x000051e0), X(0x00006d02),
  X(0x00008c03), X(0x0000aee5), X(0x0000d5a7), X(0x00010049),
  X(0x00012ecb), X(0x0001612d), X(0x00019770), X(0x0001d193),
  X(0x00020f96), X(0x00025178), X(0x0002973c), X(0x0002e0df),
  X(0x00032e62), X(0x00037fc5), X(0x0003d509), X(0x00042e2c),
  X(0x00048b30), X(0x0004ec13), X(0x000550d7), X(0x0005b97a),
  X(0x000625fe), X(0x00069661), X(0x00070aa4), X(0x000782c8),
  X(0x0007fecb), X(0x00087eae), X(0x00090271), X(0x00098a14),
  X(0x000a1597), X(0x000aa4f9), X(0x000b383b), X(0x000bcf5d),
  X(0x000c6a5f), X(0x000d0941), X(0x000dac02), X(0x000e52a3),
  X(0x000efd23), X(0x000fab84), X(0x00105dc3), X(0x001113e3),
  X(0x0011cde2), X(0x00128bc0), X(0x00134d7e), X(0x0014131b),
  X(0x0014dc98), X(0x0015a9f4), X(0x00167b30), X(0x0017504a),
  X(0x00182945), X(0x0019061e), X(0x0019e6d7), X(0x001acb6f),
  X(0x001bb3e6), X(0x001ca03c), X(0x001d9071), X(0x001e8485),
  X(0x001f7c79), X(0x0020784b), X(0x002177fc), X(0x00227b8c),
  X(0x002382fb), X(0x00248e49), X(0x00259d76), X(0x0026b081),
  X(0x0027c76b), X(0x0028e234), X(0x002a00dc), X(0x002b2361),
  X(0x002c49c6), X(0x002d7409), X(0x002ea22a), X(0x002fd42a),
  X(0x00310a08), X(0x003243c5), X(0x00338160), X(0x0034c2d9),
  X(0x00360830), X(0x00375165), X(0x00389e78), X(0x0039ef6a),
  X(0x003b4439), X(0x003c9ce6), X(0x003df971), X(0x003f59da),
  X(0x0040be20), X(0x00422645), X(0x00439247), X(0x00450226),
  X(0x004675e3), X(0x0047ed7e), X(0x004968f5), X(0x004ae84b),
  X(0x004c6b7d), X(0x004df28d), X(0x004f7d7a), X(0x00510c44),
  X(0x00529eeb), X(0x00543570), X(0x0055cfd1), X(0x00576e0f),
  X(0x00591029), X(0x005ab621), X(0x005c5ff5), X(0x005e0da6),
  X(0x005fbf33), X(0x0061749d), X(0x00632de4), X(0x0064eb06),
  X(0x0066ac05), X(0x006870e0), X(0x006a3998), X(0x006c062b),
  X(0x006dd69b), X(0x006faae6), X(0x0071830d), X(0x00735f10),
  X(0x00753eef), X(0x007722a9), X(0x00790a3f), X(0x007af5b1),
  X(0x007ce4fe), X(0x007ed826), X(0x0080cf29), X(0x0082ca08),
  X(0x0084c8c2), X(0x0086cb57), X(0x0088d1c7), X(0x008adc11),
  X(0x008cea37), X(0x008efc37), X(0x00911212), X(0x00932bc7),
  X(0x00954957), X(0x00976ac2), X(0x00999006), X(0x009bb925),
  X(0x009de61e), X(0x00a016f1), X(0x00a24b9e), X(0x00a48425),
  X(0x00a6c086), X(0x00a900c0), X(0x00ab44d4), X(0x00ad8cc2),
  X(0x00afd889), X(0x00b22829), X(0x00b47ba2), X(0x00b6d2f5),
  X(0x00b92e21), X(0x00bb8d26), X(0x00bdf004), X(0x00c056ba),
  X(0x00c2c149), X(0x00c52fb1), X(0x00c7a1f1), X(0x00ca180a),
  X(0x00cc91fb), X(0x00cf0fc5), X(0x00d19166), X(0x00d416df),
  X(0x00d6a031), X(0x00d92d5a), X(0x00dbbe5b), X(0x00de5333),
  X(0x00e0ebe3), X(0x00e3886b), X(0x00e628c9), X(0x00e8ccff),
  X(0x00eb750c), X(0x00ee20f0), X(0x00f0d0ab), X(0x00f3843d),
  X(0x00f63ba5), X(0x00f8f6e4), X(0x00fbb5fa), X(0x00fe78e5),
  X(0x01013fa7), X(0x01040a3f), X(0x0106d8ae), X(0x0109aaf2),
  X(0x010c810c), X(0x010f5afb), X(0x011238c0), X(0x01151a5b),
  X(0x0117ffcb), X(0x011ae910), X(0x011dd62a), X(0x0120c719),
  X(0x0123bbdd), X(0x0126b476), X(0x0129b0e4), X(0x012cb126),
  X(0x012fb53c), X(0x0132bd27), X(0x0135c8e6), X(0x0138d879),
  X(0x013bebdf), X(0x013f031a), X(0x01421e28), X(0x01453d0a),
  X(0x01485fbf), X(0x014b8648), X(0x014eb0a4), X(0x0151ded2),
  X(0x015510d4), X(0x015846a8), X(0x015b8050), X(0x015ebdc9),
  X(0x0161ff15), X(0x01654434), X(0x01688d24), X(0x016bd9e6),
  X(0x016f2a7b), X(0x01727ee1), X(0x0175d718), X(0x01793321),
  X(0x017c92fc), X(0x017ff6a7), X(0x01835e24), X(0x0186c972),
  X(0x018a3890), X(0x018dab7f), X(0x0191223f), X(0x01949ccf),
  X(0x01981b2f), X(0x019b9d5f), X(0x019f235f), X(0x01a2ad2f),
  X(0x01a63acf), X(0x01a9cc3e), X(0x01ad617c), X(0x01b0fa8a),
  X(0x01b49767), X(0x01b83813), X(0x01bbdc8d), X(0x01bf84d6),
  X(0x01c330ee), X(0x01c6e0d4), X(0x01ca9488), X(0x01ce4c0b),
  X(0x01d2075b), X(0x01d5c679), X(0x01d98964), X(0x01dd501d),
  X(0x01e11aa3), X(0x01e4e8f6), X(0x01e8bb17), X(0x01ec9104),
  X(0x01f06abd), X(0x01f44844), X(0x01f82996), X(0x01fc0eb5),
  X(0x01fff7a0), X(0x0203e456), X(0x0207d4d9), X(0x020bc926),
  X(0x020fc140), X(0x0213bd24), X(0x0217bcd4), X(0x021bc04e),
  X(0x021fc793), X(0x0223d2a3), X(0x0227e17d), X(0x022bf421),
  X(0x02300a90), X(0x023424c8), X(0x023842ca), X(0x023c6495),
  X(0x02408a2a), X(0x0244b389), X(0x0248e0b0), X(0x024d11a0),
  X(0x02514659), X(0x02557eda), X(0x0259bb24), X(0x025dfb35),
  X(0x02623f0f), X(0x026686b1), X(0x026ad21a), X(0x026f214b),
  X(0x02737443), X(0x0277cb02), X(0x027c2588), X(0x028083d5),
  X(0x0284e5e9), X(0x02894bc2), X(0x028db562), X(0x029222c8),
  X(0x029693f4), X(0x029b08e6), X(0x029f819d), X(0x02a3fe19),
  X(0x02a87e5b), X(0x02ad0261), X(0x02b18a2c), X(0x02b615bb),
  X(0x02baa50f), X(0x02bf3827), X(0x02c3cf03), X(0x02c869a3),
  X(0x02cd0807), X(0x02d1aa2d), X(0x02d65017), X(0x02daf9c4),
  X(0x02dfa734), X(0x02e45866), X(0x02e90d5b), X(0x02edc612),
  X(0x02f2828b), X(0x02f742c6), X(0x02fc06c3), X(0x0300ce80),
  X(0x030599ff), X(0x030a6940), X(0x030f3c40), X(0x03141302),
  X(0x0318ed84), X(0x031dcbc6), X(0x0322adc8), X(0x0327938a),
  X(0x032c7d0c), X(0x03316a4c), X(0x03365b4d), X(0x033b500c),
  X(0x03404889), X(0x034544c6), X(0x034a44c0), X(0x034f4879),
  X(0x03544ff0), X(0x03595b24), X(0x035e6a16), X(0x03637cc5),
  X(0x03689331), X(0x036dad5a), X(0x0372cb40), X(0x0377ece2),
  X(0x037d1240), X(0x03823b5a), X(0x03876830), X(0x038c98c1),
  X(0x0391cd0e), X(0x03970516), X(0x039c40d8), X(0x03a18055),
  X(0x03a6c38d), X(0x03ac0a7f), X(0x03b1552b), X(0x03b6a390),
  X(0x03bbf5af), X(0x03c14b88), X(0x03c6a519), X(0x03cc0263),
  X(0x03d16366), X(0x03d6c821), X(0x03dc3094), X(0x03e19cc0),
  X(0x03e70ca2), X(0x03ec803d), X(0x03f1f78e), X(0x03f77296),
  X(0x03fcf155), X(0x040273cb), X(0x0407f9f7), X(0x040d83d9),
  X(0x04131170), X(0x0418a2bd), X(0x041e37c0), X(0x0423d077),
  X(0x04296ce4), X(0x042f0d04), X(0x0434b0da), X(0x043a5863),
  X(0x044003a0), X(0x0445b290), X(0x044b6534), X(0x04511b8b),
  X(0x0456d595), X(0x045c9352), X(0x046254c1), X(0x046819e1),
  X(0x046de2b4), X(0x0473af39), X(0x04797f6e), X(0x047f5355),
  X(0x04852aec), X(0x048b0635), X(0x0490e52d), X(0x0496c7d6),
  X(0x049cae2e), X(0x04a29836), X(0x04a885ed), X(0x04ae7753),
  X(0x04b46c68), X(0x04ba652b), X(0x04c0619d), X(0x04c661bc),
  X(0x04cc658a), X(0x04d26d04), X(0x04d8782c), X(0x04de8701),
  X(0x04e49983), X(0x04eaafb0), X(0x04f0c98a), X(0x04f6e710),
  X(0x04fd0842), X(0x05032d1e), X(0x050955a6), X(0x050f81d8),
  X(0x0515b1b5), X(0x051be53d), X(0x05221c6e), X(0x05285748),
  X(0x052e95cd), X(0x0534d7fa), X(0x053b1dd0), X(0x0541674e),
  X(0x0547b475), X(0x054e0544), X(0x055459bb), X(0x055ab1d9),
  X(0x05610d9e), X(0x05676d0a), X(0x056dd01c), X(0x057436d5),
  X(0x057aa134), X(0x05810f38), X(0x058780e2), X(0x058df631),
  X(0x05946f25), X(0x059aebbe), X(0x05a16bfa), X(0x05a7efdb),
  X(0x05ae775f), X(0x05b50287), X(0x05bb9152), X(0x05c223c0),
  X(0x05c8b9d0), X(0x05cf5382), X(0x05d5f0d6), X(0x05dc91cc),
  X(0x05e33663), X(0x05e9de9c), X(0x05f08a75), X(0x05f739ee),
  X(0x05fded07), X(0x0604a3c0), X(0x060b5e19), X(0x06121c11),
  X(0x0618dda8), X(0x061fa2dd), X(0x06266bb1), X(0x062d3822),
  X(0x06340831), X(0x063adbde), X(0x0641b328), X(0x06488e0e),
  X(0x064f6c91), X(0x06564eaf), X(0x065d346a), X(0x06641dc0),
  X(0x066b0ab1), X(0x0671fb3d), X(0x0678ef64), X(0x067fe724),
  X(0x0686e27f), X(0x068de173), X(0x0694e400), X(0x069bea27),
  X(0x06a2f3e6), X(0x06aa013d), X(0x06b1122c), X(0x06b826b3),
  X(0x06bf3ed1), X(0x06c65a86), X(0x06cd79d1), X(0x06d49cb3),
  X(0x06dbc32b), X(0x06e2ed38), X(0x06ea1adb), X(0x06f14c13),
  X(0x06f880df), X(0x06ffb940), X(0x0706f535), X(0x070e34bd),
  X(0x071577d9), X(0x071cbe88), X(0x072408c9), X(0x072b569d),
  X(0x0732a802), X(0x0739fcf9), X(0x07415582), X(0x0748b19b),
  X(0x07501145), X(0x0757747f), X(0x075edb49), X(0x076645a3),
  X(0x076db38c), X(0x07752503), X(0x077c9a09), X(0x0784129e),
  X(0x078b8ec0), X(0x07930e70), X(0x079a91ac), X(0x07a21876),
  X(0x07a9a2cc), X(0x07b130ad), X(0x07b8c21b), X(0x07c05714),
  X(0x07c7ef98), X(0x07cf8ba6), X(0x07d72b3f), X(0x07dece62),
  X(0x07e6750e), X(0x07ee1f43), X(0x07f5cd01), X(0x07fd7e48),
  X(0x08053316), X(0x080ceb6d), X(0x0814a74a), X(0x081c66af),
  X(0x0824299a), X(0x082bf00c), X(0x0833ba03), X(0x083b8780),
  X(0x08435882), X(0x084b2d09), X(0x08530514), X(0x085ae0a3),
  X(0x0862bfb6), X(0x086aa24c), X(0x08728865), X(0x087a7201),
  X(0x08825f1e), X(0x088a4fbe), X(0x089243de), X(0x089a3b80),
  X(0x08a236a2), X(0x08aa3545), X(0x08b23767), X(0x08ba3d09),
  X(0x08c2462a), X(0x08ca52c9), X(0x08d262e7), X(0x08da7682),
  X(0x08e28d9c), X(0x08eaa832), X(0x08f2c645), X(0x08fae7d4),
  X(0x09030cdf), X(0x090b3566), X(0x09136168), X(0x091b90e5),
  X(0x0923c3dc), X(0x092bfa4d), X(0x09343437), X(0x093c719b),
  X(0x0944b277), X(0x094cf6cc), X(0x09553e99), X(0x095d89dd),
  X(0x0965d899), X(0x096e2acb), X(0x09768073), X(0x097ed991),
  X(0x09873625), X(0x098f962e), X(0x0997f9ac), X(0x09a0609e),
  X(0x09a8cb04), X(0x09b138dd), X(0x09b9aa29), X(0x09c21ee8),
  X(0x09ca9719), X(0x09d312bc), X(0x09db91d0), X(0x09e41456),
  X(0x09ec9a4b), X(0x09f523b1), X(0x09fdb087), X(0x0a0640cc),
  X(0x0a0ed47f), X(0x0a176ba2), X(0x0a200632), X(0x0a28a42f),
  X(0x0a31459a), X(0x0a39ea72), X(0x0a4292b5), X(0x0a4b3e65),
  X(0x0a53ed80), X(0x0a5ca006), X(0x0a6555f7), X(0x0a6e0f51),
  X(0x0a76cc16), X(0x0a7f8c44), X(0x0a884fda), X(0x0a9116d9),
  X(0x0a99e140), X(0x0aa2af0e), X(0x0aab8043), X(0x0ab454df),
  X(0x0abd2ce1), X(0x0ac60849), X(0x0acee716), X(0x0ad7c948),
  X(0x0ae0aedf), X(0x0ae997d9), X(0x0af28437), X(0x0afb73f7),
  X(0x0b04671b), X(0x0b0d5da0), X(0x0b165788), X(0x0b1f54d0),
  X(0x0b285579), X(0x0b315983), X(0x0b3a60ec), X(0x0b436bb5),
  X(0x0b4c79dd), X(0x0b558b63), X(0x0b5ea048), X(0x0b67b88a),
  X(0x0b70d429), X(0x0b79f324), X(0x0b83157c), X(0x0b8c3b30),
  X(0x0b95643f), X(0x0b9e90a8), X(0x0ba7c06c), X(0x0bb0f38a),
  X(0x0bba2a01), X(0x0bc363d1), X(0x0bcca0f9), X(0x0bd5e17a),
  X(0x0bdf2552), X(0x0be86c81), X(0x0bf1b706), X(0x0bfb04e2),
  X(0x0c045613), X(0x0c0daa99), X(0x0c170274), X(0x0c205da3),
  X(0x0c29bc25), X(0x0c331dfb), X(0x0c3c8323), X(0x0c45eb9e),
  X(0x0c4f576a), X(0x0c58c688), X(0x0c6238f6), X(0x0c6baeb5),
  X(0x0c7527c3), X(0x0c7ea421), X(0x0c8823cd), X(0x0c91a6c8),
  X(0x0c9b2d10), X(0x0ca4b6a6), X(0x0cae4389), X(0x0cb7d3b8),
  X(0x0cc16732), X(0x0ccafdf8), X(0x0cd49809), X(0x0cde3564),
  X(0x0ce7d609), X(0x0cf179f7), X(0x0cfb212e), X(0x0d04cbad),
  X(0x0d0e7974), X(0x0d182a83), X(0x0d21ded8), X(0x0d2b9673),
  X(0x0d355154), X(0x0d3f0f7b), X(0x0d48d0e6), X(0x0d529595),
  X(0x0d5c5d88), X(0x0d6628be), X(0x0d6ff737), X(0x0d79c8f2),
  X(0x0d839dee), X(0x0d8d762c), X(0x0d9751aa), X(0x0da13068),
  X(0x0dab1266), X(0x0db4f7a3), X(0x0dbee01e), X(0x0dc8cbd8),
  X(0x0dd2bace), X(0x0ddcad02), X(0x0de6a272), X(0x0df09b1e),
  X(0x0dfa9705), X(0x0e049627), X(0x0e0e9883), X(0x0e189e19),
  X(0x0e22a6e8), X(0x0e2cb2f0), X(0x0e36c230), X(0x0e40d4a8),
  X(0x0e4aea56), X(0x0e55033b), X(0x0e5f1f56), X(0x0e693ea7),
  X(0x0e73612c), X(0x0e7d86e5), X(0x0e87afd3), X(0x0e91dbf3),
  X(0x0e9c0b47), X(0x0ea63dcc), X(0x0eb07383), X(0x0ebaac6b),
  X(0x0ec4e883), X(0x0ecf27cc), X(0x0ed96a44), X(0x0ee3afea),
  X(0x0eedf8bf), X(0x0ef844c2), X(0x0f0293f2), X(0x0f0ce64e),
  X(0x0f173bd6), X(0x0f21948a), X(0x0f2bf069), X(0x0f364f72),
  X(0x0f40b1a5), X(0x0f4b1701), X(0x0f557f86), X(0x0f5feb32),
  X(0x0f6a5a07), X(0x0f74cc02), X(0x0f7f4124), X(0x0f89b96b),
  X(0x0f9434d8), X(0x0f9eb369), X(0x0fa9351e), X(0x0fb3b9f7),
  X(0x0fbe41f3), X(0x0fc8cd11), X(0x0fd35b51), X(0x0fddecb2),
  X(0x0fe88134), X(0x0ff318d6), X(0x0ffdb397), X(0x10085177),
  X(0x1012f275), X(0x101d9691), X(0x10283dca), X(0x1032e81f),
  X(0x103d9591), X(0x1048461e), X(0x1052f9c5), X(0x105db087),
  X(0x10686a62), X(0x10732756), X(0x107de763), X(0x1088aa87),
  X(0x109370c2), X(0x109e3a14), X(0x10a9067c), X(0x10b3d5f9),
  X(0x10bea88b), X(0x10c97e31), X(0x10d456eb), X(0x10df32b8),
  X(0x10ea1197), X(0x10f4f387), X(0x10ffd889), X(0x110ac09b),
  X(0x1115abbe), X(0x112099ef), X(0x112b8b2f), X(0x11367f7d),
  X(0x114176d9), X(0x114c7141), X(0x11576eb6), X(0x11626f36),
  X(0x116d72c1), X(0x11787957), X(0x118382f6), X(0x118e8f9e),
  X(0x11999f4f), X(0x11a4b208), X(0x11afc7c7), X(0x11bae08e),
  X(0x11c5fc5a), X(0x11d11b2c), X(0x11dc3d02), X(0x11e761dd),
  X(0x11f289ba), X(0x11fdb49b), X(0x1208e27e), X(0x12141362),
  X(0x121f4748), X(0x122a7e2d), X(0x1235b812), X(0x1240f4f6),
  X(0x124c34d9), X(0x125777b9), X(0x1262bd96), X(0x126e0670),
  X(0x12795245), X(0x1284a115), X(0x128ff2e0), X(0x129b47a5),
  X(0x12a69f63), X(0x12b1fa19), X(0x12bd57c7), X(0x12c8b86c),
  X(0x12d41c08), X(0x12df829a), X(0x12eaec21), X(0x12f6589d),
  X(0x1301c80c), X(0x130d3a6f), X(0x1318afc4), X(0x1324280b),
  X(0x132fa344), X(0x133b216d), X(0x1346a286), X(0x1352268e),
  X(0x135dad85), X(0x1369376a), X(0x1374c43c), X(0x138053fb),
  X(0x138be6a5), X(0x13977c3b), X(0x13a314bc), X(0x13aeb026),
  X(0x13ba4e79), X(0x13c5efb5), X(0x13d193d9), X(0x13dd3ae4),
  X(0x13e8e4d6), X(0x13f491ad), X(0x1400416a), X(0x140bf40b),
  X(0x1417a98f), X(0x142361f7), X(0x142f1d41), X(0x143adb6d),
  X(0x14469c7a), X(0x14526067), X(0x145e2734), X(0x1469f0df),
  X(0x1475bd69), X(0x14818cd0), X(0x148d5f15), X(0x14993435),
  X(0x14a50c31), X(0x14b0e708), X(0x14bcc4b8), X(0x14c8a542),
  X(0x14d488a5), X(0x14e06edf), X(0x14ec57f1), X(0x14f843d9),
  X(0x15043297), X(0x1510242b), X(0x151c1892), X(0x15280fcd),
  X(0x153409dc), X(0x154006bc), X(0x154c066e), X(0x155808f1),
  X(0x15640e44), X(0x15701666), X(0x157c2157), X(0x15882f16),
  X(0x15943fa2), X(0x15a052fb), X(0x15ac691f), X(0x15b8820f),
  X(0x15c49dc8), X(0x15d0bc4c), X(0x15dcdd98), X(0x15e901ad),
  X(0x15f52888), X(0x1601522b), X(0x160d7e93), X(0x1619adc1),
  X(0x1625dfb3), X(0x16321469), X(0x163e4be2), X(0x164a861d),
  X(0x1656c31a), X(0x166302d8), X(0x166f4555), X(0x167b8a92),
  X(0x1687d28e), X(0x16941d47), X(0x16a06abe), X(0x16acbaf0),
  X(0x16b90ddf), X(0x16c56388), X(0x16d1bbeb), X(0x16de1708),
  X(0x16ea74dd), X(0x16f6d56a), X(0x170338ae), X(0x170f9ea8),
  X(0x171c0758), X(0x172872bd), X(0x1734e0d6), X(0x174151a2),
  X(0x174dc520), X(0x175a3b51), X(0x1766b432), X(0x17732fc4),
  X(0x177fae05), X(0x178c2ef4), X(0x1798b292), X(0x17a538dd),
  X(0x17b1c1d4), X(0x17be4d77), X(0x17cadbc5), X(0x17d76cbc),
  X(0x17e4005e), X(0x17f096a7), X(0x17fd2f98), X(0x1809cb31),
  X(0x1816696f), X(0x18230a53), X(0x182faddc), X(0x183c5408),
  X(0x1848fcd8), X(0x1855a849), X(0x1862565d), X(0x186f0711),
  X(0x187bba64), X(0x18887057), X(0x189528e9), X(0x18a1e418),
  X(0x18aea1e3), X(0x18bb624b), X(0x18c8254e), X(0x18d4eaeb),
  X(0x18e1b321), X(0x18ee7df1), X(0x18fb4b58), X(0x19081b57),
  X(0x1914edec), X(0x1921c317), X(0x192e9ad6), X(0x193b7529),
  X(0x19485210), X(0x19553189), X(0x19621393), X(0x196ef82e),
  X(0x197bdf59), X(0x1988c913), X(0x1995b55c), X(0x19a2a432),
  X(0x19af9595), X(0x19bc8983), X(0x19c97ffd), X(0x19d67900),
  X(0x19e3748e), X(0x19f072a3), X(0x19fd7341), X(0x1a0a7665),
  X(0x1a177c10), X(0x1a248440), X(0x1a318ef4), X(0x1a3e9c2c),
  X(0x1a4babe7), X(0x1a58be24), X(0x1a65d2e2), X(0x1a72ea20),
  X(0x1a8003de), X(0x1a8d201a), X(0x1a9a3ed5), X(0x1aa7600c),
  X(0x1ab483bf), X(0x1ac1a9ee), X(0x1aced297), X(0x1adbfdba),
  X(0x1ae92b56), X(0x1af65b69), X(0x1b038df4), X(0x1b10c2f5),
  X(0x1b1dfa6b), X(0x1b2b3456), X(0x1b3870b5), X(0x1b45af87),
  X(0x1b52f0ca), X(0x1b60347f), X(0x1b6d7aa4), X(0x1b7ac339),
  X(0x1b880e3c), X(0x1b955bad), X(0x1ba2ab8b), X(0x1baffdd5),
  X(0x1bbd528a), X(0x1bcaa9a9), X(0x1bd80332), X(0x1be55f24),
  X(0x1bf2bd7d), X(0x1c001e3d), X(0x1c0d8164), X(0x1c1ae6ef),
  X(0x1c284edf), X(0x1c35b932), X(0x1c4325e7), X(0x1c5094fe),
  X(0x1c5e0677), X(0x1c6b7a4f), X(0x1c78f086), X(0x1c86691b),
  X(0x1c93e40d), X(0x1ca1615c), X(0x1caee107), X(0x1cbc630c),
  X(0x1cc9e76b), X(0x1cd76e23), X(0x1ce4f733), X(0x1cf2829a),
  X(0x1d001057), X(0x1d0da06a), X(0x1d1b32d1), X(0x1d28c78c),
  X(0x1d365e9a), X(0x1d43f7f9), X(0x1d5193a9), X(0x1d5f31aa),
  X(0x1d6cd1f9), X(0x1d7a7497), X(0x1d881982), X(0x1d95c0ba),
  X(0x1da36a3d), X(0x1db1160a), X(0x1dbec422), X(0x1dcc7482),
  X(0x1dda272b), X(0x1de7dc1a), X(0x1df59350), X(0x1e034ccb),
  X(0x1e11088a), X(0x1e1ec68c), X(0x1e2c86d1), X(0x1e3a4958),
  X(0x1e480e20), X(0x1e55d527), X(0x1e639e6d), X(0x1e7169f1),
  X(0x1e7f37b2), X(0x1e8d07b0), X(0x1e9ad9e8), X(0x1ea8ae5b),
  X(0x1eb68507), X(0x1ec45dec), X(0x1ed23908), X(0x1ee0165b),
  X(0x1eedf5e4), X(0x1efbd7a1), X(0x1f09bb92), X(0x1f17a1b6),
  X(0x1f258a0d), X(0x1f337494), X(0x1f41614b), X(0x1f4f5032),
  X(0x1f5d4147), X(0x1f6b3489), X(0x1f7929f7), X(0x1f872192),
  X(0x1f951b56), X(0x1fa31744), X(0x1fb1155b), X(0x1fbf159a),
  X(0x1fcd17ff), X(0x1fdb1c8b), X(0x1fe9233b), X(0x1ff72c0f),
  X(0x20053706), X(0x20134420), X(0x2021535a), X(0x202f64b4),
  X(0x203d782e), X(0x204b8dc6), X(0x2059a57c), X(0x2067bf4e),
  X(0x2075db3b), X(0x2083f943), X(0x20921964), X(0x20a03b9e),
  X(0x20ae5fef), X(0x20bc8657), X(0x20caaed5), X(0x20d8d967),
  X(0x20e7060e), X(0x20f534c7), X(0x21036592), X(0x2111986e),
  X(0x211fcd59), X(0x212e0454), X(0x213c3d5d), X(0x214a7873),
  X(0x2158b594), X(0x2166f4c1), X(0x217535f8), X(0x21837938),
  X(0x2191be81), X(0x21a005d0), X(0x21ae4f26), X(0x21bc9a81),
  X(0x21cae7e0), X(0x21d93743), X(0x21e788a8), X(0x21f5dc0e),
  X(0x22043174), X(0x221288da), X(0x2220e23e), X(0x222f3da0),
  X(0x223d9afe), X(0x224bfa58), X(0x225a5bac), X(0x2268bef9),
  X(0x2277243f), X(0x22858b7d), X(0x2293f4b0), X(0x22a25fda),
  X(0x22b0ccf8), X(0x22bf3c09), X(0x22cdad0d), X(0x22dc2002),
  X(0x22ea94e8), X(0x22f90bbe), X(0x23078482), X(0x2315ff33),
  X(0x23247bd1), X(0x2332fa5b), X(0x23417acf), X(0x234ffd2c),
  X(0x235e8173), X(0x236d07a0), X(0x237b8fb4), X(0x238a19ae),
  X(0x2398a58c), X(0x23a7334d), X(0x23b5c2f1), X(0x23c45477),
  X(0x23d2e7dd), X(0x23e17d22), X(0x23f01446), X(0x23fead47),
  X(0x240d4825), X(0x241be4dd), X(0x242a8371), X(0x243923dd),
  X(0x2447c622), X(0x24566a3e), X(0x24651031), X(0x2473b7f8),
  X(0x24826194), X(0x24910d03), X(0x249fba44), X(0x24ae6957),
  X(0x24bd1a39), X(0x24cbccea), X(0x24da816a), X(0x24e937b7),
  X(0x24f7efcf), X(0x2506a9b3), X(0x25156560), X(0x252422d6),
  X(0x2532e215), X(0x2541a31a), X(0x255065e4), X(0x255f2a74),
  X(0x256df0c7), X(0x257cb8dd), X(0x258b82b4), X(0x259a4e4c),
  X(0x25a91ba4), X(0x25b7eaba), X(0x25c6bb8e), X(0x25d58e1e),
  X(0x25e46269), X(0x25f3386e), X(0x2602102d), X(0x2610e9a4),
  X(0x261fc4d3), X(0x262ea1b7), X(0x263d8050), X(0x264c609e),
  X(0x265b429e), X(0x266a2650), X(0x26790bb3), X(0x2687f2c6),
  X(0x2696db88), X(0x26a5c5f7), X(0x26b4b213), X(0x26c39fda),
  X(0x26d28f4c), X(0x26e18067), X(0x26f0732b), X(0x26ff6796),
  X(0x270e5da7), X(0x271d555d), X(0x272c4eb7), X(0x273b49b5),
  X(0x274a4654), X(0x27594495), X(0x27684475), X(0x277745f4),
  X(0x27864910), X(0x27954dc9), X(0x27a4541e), X(0x27b35c0d),
  X(0x27c26596), X(0x27d170b7), X(0x27e07d6f), X(0x27ef8bbd),
  X(0x27fe9ba0), X(0x280dad18), X(0x281cc022), X(0x282bd4be),
  X(0x283aeaeb), X(0x284a02a7), X(0x28591bf2), X(0x286836cb),
  X(0x28775330), X(0x28867120), X(0x2895909b), X(0x28a4b19e),
  X(0x28b3d42a), X(0x28c2f83d), X(0x28d21dd5), X(0x28e144f3),
  X(0x28f06d94), X(0x28ff97b8), X(0x290ec35d), X(0x291df082),
  X(0x292d1f27), X(0x293c4f4a), X(0x294b80eb), X(0x295ab407),
  X(0x2969e89e), X(0x29791eaf), X(0x29885639), X(0x29978f3b),
  X(0x29a6c9b3), X(0x29b605a0), X(0x29c54302), X(0x29d481d7),
  X(0x29e3c21e), X(0x29f303d6), X(0x2a0246fd), X(0x2a118b94),
  X(0x2a20d198), X(0x2a301909), X(0x2a3f61e6), X(0x2a4eac2c),
  X(0x2a5df7dc), X(0x2a6d44f4), X(0x2a7c9374), X(0x2a8be359),
  X(0x2a9b34a2), X(0x2aaa8750), X(0x2ab9db60), X(0x2ac930d1),
  X(0x2ad887a3), X(0x2ae7dfd3), X(0x2af73962), X(0x2b06944e),
  X(0x2b15f096), X(0x2b254e38), X(0x2b34ad34), X(0x2b440d89),
  X(0x2b536f34), X(0x2b62d236), X(0x2b72368d), X(0x2b819c38),
  X(0x2b910336), X(0x2ba06b86), X(0x2bafd526), X(0x2bbf4015),
  X(0x2bceac53), X(0x2bde19de), X(0x2bed88b5), X(0x2bfcf8d7),
  X(0x2c0c6a43), X(0x2c1bdcf7), X(0x2c2b50f3), X(0x2c3ac635),
  X(0x2c4a3cbd), X(0x2c59b488), X(0x2c692d97), X(0x2c78a7e7),
  X(0x2c882378), X(0x2c97a049), X(0x2ca71e58), X(0x2cb69da4),
  X(0x2cc61e2c), X(0x2cd59ff0), X(0x2ce522ed), X(0x2cf4a723),
  X(0x2d042c90), X(0x2d13b334), X(0x2d233b0d), X(0x2d32c41a),
  X(0x2d424e5a), X(0x2d51d9cc), X(0x2d61666e), X(0x2d70f440),
  X(0x2d808340), X(0x2d90136e), X(0x2d9fa4c7), X(0x2daf374c),
  X(0x2dbecafa), X(0x2dce5fd1), X(0x2dddf5cf), X(0x2ded8cf4),
  X(0x2dfd253d), X(0x2e0cbeab), X(0x2e1c593b), X(0x2e2bf4ed),
  X(0x2e3b91c0), X(0x2e4b2fb1), X(0x2e5acec1), X(0x2e6a6eee),
  X(0x2e7a1037), X(0x2e89b29b), X(0x2e995618), X(0x2ea8faad),
  X(0x2eb8a05a), X(0x2ec8471c), X(0x2ed7eef4), X(0x2ee797df),
  X(0x2ef741dc), X(0x2f06eceb), X(0x2f16990a), X(0x2f264639),
  X(0x2f35f475), X(0x2f45a3bd), X(0x2f555412), X(0x2f650570),
  X(0x2f74b7d8), X(0x2f846b48), X(0x2f941fbe), X(0x2fa3d53a),
  X(0x2fb38bbb), X(0x2fc3433f), X(0x2fd2fbc5), X(0x2fe2b54c),
  X(0x2ff26fd3), X(0x30022b58), X(0x3011e7db), X(0x3021a55a),
  X(0x303163d4), X(0x30412348), X(0x3050e3b5), X(0x3060a519),
  X(0x30706773), X(0x30802ac3), X(0x308fef06), X(0x309fb43d),
  X(0x30af7a65), X(0x30bf417d), X(0x30cf0985), X(0x30ded27a),
  X(0x30ee9c5d), X(0x30fe672b), X(0x310e32e3), X(0x311dff85),
  X(0x312dcd0f), X(0x313d9b80), X(0x314d6ad7), X(0x315d3b12),
  X(0x316d0c30), X(0x317cde31), X(0x318cb113), X(0x319c84d4),
  X(0x31ac5974), X(0x31bc2ef1), X(0x31cc054b), X(0x31dbdc7f),
  X(0x31ebb48e), X(0x31fb8d74), X(0x320b6733), X(0x321b41c7),
  X(0x322b1d31), X(0x323af96e), X(0x324ad67e), X(0x325ab45f),
  X(0x326a9311), X(0x327a7291), X(0x328a52e0), X(0x329a33fb),
  X(0x32aa15e1), X(0x32b9f892), X(0x32c9dc0c), X(0x32d9c04d),
  X(0x32e9a555), X(0x32f98b22), X(0x330971b4), X(0x33195909),
  X(0x3329411f), X(0x333929f6), X(0x3349138c), X(0x3358fde1),
  X(0x3368e8f2), X(0x3378d4c0), X(0x3388c147), X(0x3398ae89),
  X(0x33a89c82), X(0x33b88b32), X(0x33c87a98), X(0x33d86ab2),
  X(0x33e85b80), X(0x33f84d00), X(0x34083f30), X(0x34183210),
  X(0x3428259f), X(0x343819db), X(0x34480ec3), X(0x34580455),
  X(0x3467fa92), X(0x3477f176), X(0x3487e902), X(0x3497e134),
  X(0x34a7da0a), X(0x34b7d384), X(0x34c7cda0), X(0x34d7c85e),
  X(0x34e7c3bb), X(0x34f7bfb7), X(0x3507bc50), X(0x3517b985),
  X(0x3527b756), X(0x3537b5c0), X(0x3547b4c3), X(0x3557b45d),
  X(0x3567b48d), X(0x3577b552), X(0x3587b6aa), X(0x3597b895),
  X(0x35a7bb12), X(0x35b7be1e), X(0x35c7c1b9), X(0x35d7c5e1),
  X(0x35e7ca96), X(0x35f7cfd6), X(0x3607d5a0), X(0x3617dbf3),
  X(0x3627e2cd), X(0x3637ea2d), X(0x3647f212), X(0x3657fa7b),
  X(0x36680366), X(0x36780cd2), X(0x368816bf), X(0x3698212b),
  X(0x36a82c14), X(0x36b83779), X(0x36c8435a), X(0x36d84fb4),
  X(0x36e85c88), X(0x36f869d2), X(0x37087793), X(0x371885c9),
  X(0x37289473), X(0x3738a38f), X(0x3748b31d), X(0x3758c31a),
  X(0x3768d387), X(0x3778e461), X(0x3788f5a7), X(0x37990759),
  X(0x37a91975), X(0x37b92bf9), X(0x37c93ee4), X(0x37d95236),
  X(0x37e965ed), X(0x37f97a08), X(0x38098e85), X(0x3819a363),
  X(0x3829b8a2), X(0x3839ce3f), X(0x3849e43a), X(0x3859fa91),
  X(0x386a1143), X(0x387a284f), X(0x388a3fb4), X(0x389a5770),
  X(0x38aa6f83), X(0x38ba87ea), X(0x38caa0a5), X(0x38dab9b2),
  X(0x38ead311), X(0x38faecbf), X(0x390b06bc), X(0x391b2107),
  X(0x392b3b9e), X(0x393b5680), X(0x394b71ac), X(0x395b8d20),
  X(0x396ba8dc), X(0x397bc4dd), X(0x398be124), X(0x399bfdae),
  X(0x39ac1a7a), X(0x39bc3788), X(0x39cc54d5), X(0x39dc7261),
  X(0x39ec902a), X(0x39fcae2f), X(0x3a0ccc70), X(0x3a1ceaea),
  X(0x3a2d099c), X(0x3a3d2885), X(0x3a4d47a5), X(0x3a5d66f9),
  X(0x3a6d8680), X(0x3a7da63a), X(0x3a8dc625), X(0x3a9de63f),
  X(0x3aae0688), X(0x3abe26fe), X(0x3ace47a0), X(0x3ade686d),
  X(0x3aee8963), X(0x3afeaa82), X(0x3b0ecbc7), X(0x3b1eed32),
  X(0x3b2f0ec2), X(0x3b3f3075), X(0x3b4f524a), X(0x3b5f7440),
  X(0x3b6f9656), X(0x3b7fb889), X(0x3b8fdada), X(0x3b9ffd46),
  X(0x3bb01fce), X(0x3bc0426e), X(0x3bd06526), X(0x3be087f6),
  X(0x3bf0aada), X(0x3c00cdd4), X(0x3c10f0e0), X(0x3c2113fe),
  X(0x3c31372d), X(0x3c415a6b), X(0x3c517db7), X(0x3c61a110),
  X(0x3c71c475), X(0x3c81e7e4), X(0x3c920b5c), X(0x3ca22edc),
  X(0x3cb25262), X(0x3cc275ee), X(0x3cd2997e), X(0x3ce2bd11),
  X(0x3cf2e0a6), X(0x3d03043b), X(0x3d1327cf), X(0x3d234b61),
  X(0x3d336ef0), X(0x3d43927a), X(0x3d53b5ff), X(0x3d63d97c),
  X(0x3d73fcf1), X(0x3d84205c), X(0x3d9443bd), X(0x3da46711),
  X(0x3db48a58), X(0x3dc4ad91), X(0x3dd4d0ba), X(0x3de4f3d1),
  X(0x3df516d7), X(0x3e0539c9), X(0x3e155ca6), X(0x3e257f6d),
  X(0x3e35a21d), X(0x3e45c4b4), X(0x3e55e731), X(0x3e660994),
  X(0x3e762bda), X(0x3e864e03), X(0x3e96700d), X(0x3ea691f7),
  X(0x3eb6b3bf), X(0x3ec6d565), X(0x3ed6f6e8), X(0x3ee71845),
  X(0x3ef7397c), X(0x3f075a8c), X(0x3f177b73), X(0x3f279c30),
  X(0x3f37bcc2), X(0x3f47dd27), X(0x3f57fd5f), X(0x3f681d68),
  X(0x3f783d40), X(0x3f885ce7), X(0x3f987c5c), X(0x3fa89b9c),
  X(0x3fb8baa7), X(0x3fc8d97c), X(0x3fd8f819), X(0x3fe9167e),
  X(0x3ff934a8), X(0x40095296), X(0x40197049), X(0x40298dbd),
  X(0x4039aaf2), X(0x4049c7e7), X(0x4059e49a), X(0x406a010a),
  X(0x407a1d36), X(0x408a391d), X(0x409a54bd), X(0x40aa7015),
  X(0x40ba8b25), X(0x40caa5ea), X(0x40dac063), X(0x40eada90),
  X(0x40faf46e), X(0x410b0dfe), X(0x411b273d), X(0x412b402a),
  X(0x413b58c4), X(0x414b710a), X(0x415b88fa), X(0x416ba093),
  X(0x417bb7d5), X(0x418bcebe), X(0x419be54c), X(0x41abfb7e),
  X(0x41bc1153), X(0x41cc26ca), X(0x41dc3be2), X(0x41ec5099),
  X(0x41fc64ef), X(0x420c78e1), X(0x421c8c6f), X(0x422c9f97),
  X(0x423cb258), X(0x424cc4b2), X(0x425cd6a2), X(0x426ce827),
  X(0x427cf941), X(0x428d09ee), X(0x429d1a2c), X(0x42ad29fb),
  X(0x42bd3959), X(0x42cd4846), X(0x42dd56bf), X(0x42ed64c3),
  X(0x42fd7252), X(0x430d7f6a), X(0x431d8c0a), X(0x432d9831),
  X(0x433da3dd), X(0x434daf0d), X(0x435db9c0), X(0x436dc3f5),
  X(0x437dcdab), X(0x438dd6df), X(0x439ddf92), X(0x43ade7c1),
  X(0x43bdef6c), X(0x43cdf691), X(0x43ddfd2f), X(0x43ee0345),
  X(0x43fe08d2), X(0x440e0dd4), X(0x441e124b), X(0x442e1634),
  X(0x443e198f), X(0x444e1c5a), X(0x445e1e95), X(0x446e203e),
  X(0x447e2153), X(0x448e21d5), X(0x449e21c0), X(0x44ae2115),
  X(0x44be1fd1), X(0x44ce1df4), X(0x44de1b7d), X(0x44ee186a),
  X(0x44fe14ba), X(0x450e106b), X(0x451e0b7e), X(0x452e05ef),
  X(0x453dffbf), X(0x454df8eb), X(0x455df173), X(0x456de956),
  X(0x457de092), X(0x458dd726), X(0x459dcd10), X(0x45adc251),
  X(0x45bdb6e5), X(0x45cdaacd), X(0x45dd9e06), X(0x45ed9091),
  X(0x45fd826a), X(0x460d7392), X(0x461d6407), X(0x462d53c8),
  X(0x463d42d4), X(0x464d3129), X(0x465d1ec6), X(0x466d0baa),
  X(0x467cf7d3), X(0x468ce342), X(0x469ccdf3), X(0x46acb7e7),
  X(0x46bca11c), X(0x46cc8990), X(0x46dc7143), X(0x46ec5833),
  X(0x46fc3e5f), X(0x470c23c6), X(0x471c0867), X(0x472bec40),
  X(0x473bcf50), X(0x474bb196), X(0x475b9311), X(0x476b73c0),
  X(0x477b53a1), X(0x478b32b4), X(0x479b10f6), X(0x47aaee67),
  X(0x47bacb06), X(0x47caa6d1), X(0x47da81c7), X(0x47ea5be7),
  X(0x47fa3530), X(0x480a0da1), X(0x4819e537), X(0x4829bbf3),
  X(0x483991d3), X(0x484966d6), X(0x48593afb), X(0x48690e3f),
  X(0x4878e0a3), X(0x4888b225), X(0x489882c4), X(0x48a8527e),
  X(0x48b82153), X(0x48c7ef41), X(0x48d7bc47), X(0x48e78863),
  X(0x48f75396), X(0x49071ddc), X(0x4916e736), X(0x4926afa2),
  X(0x4936771f), X(0x49463dac), X(0x49560347), X(0x4965c7ef),
  X(0x49758ba4), X(0x49854e63), X(0x4995102c), X(0x49a4d0fe),
  X(0x49b490d7), X(0x49c44fb6), X(0x49d40d9a), X(0x49e3ca82),
  X(0x49f3866c), X(0x4a034159), X(0x4a12fb45), X(0x4a22b430),
  X(0x4a326c19), X(0x4a4222ff), X(0x4a51d8e1), X(0x4a618dbd),
  X(0x4a714192), X(0x4a80f45f), X(0x4a90a623), X(0x4aa056dd),
  X(0x4ab0068b), X(0x4abfb52c), X(0x4acf62c0), X(0x4adf0f44),
  X(0x4aeebab9), X(0x4afe651c), X(0x4b0e0e6c), X(0x4b1db6a9),
  X(0x4b2d5dd1), X(0x4b3d03e2), X(0x4b4ca8dd), X(0x4b5c4cbf),
  X(0x4b6bef88), X(0x4b7b9136), X(0x4b8b31c8), X(0x4b9ad13d),
  X(0x4baa6f93), X(0x4bba0ccb), X(0x4bc9a8e2), X(0x4bd943d7),
  X(0x4be8dda9), X(0x4bf87658), X(0x4c080de1), X(0x4c17a444),
  X(0x4c27397f), X(0x4c36cd92), X(0x4c46607b), X(0x4c55f239),
  X(0x4c6582cb), X(0x4c75122f), X(0x4c84a065), X(0x4c942d6c),
  X(0x4ca3b942), X(0x4cb343e6), X(0x4cc2cd57), X(0x4cd25594),
  X(0x4ce1dc9c), X(0x4cf1626d), X(0x4d00e707), X(0x4d106a68),
  X(0x4d1fec8f), X(0x4d2f6d7a), X(0x4d3eed2a), X(0x4d4e6b9d),
  X(0x4d5de8d1), X(0x4d6d64c5), X(0x4d7cdf79), X(0x4d8c58eb),
  X(0x4d9bd11a), X(0x4dab4804), X(0x4dbabdaa), X(0x4dca3209),
  X(0x4dd9a520), X(0x4de916ef), X(0x4df88774), X(0x4e07f6ae),
  X(0x4e17649c), X(0x4e26d13c), X(0x4e363c8f), X(0x4e45a692),
  X(0x4e550f44), X(0x4e6476a4), X(0x4e73dcb2), X(0x4e83416c),
  X(0x4e92a4d1), X(0x4ea206df), X(0x4eb16796), X(0x4ec0c6f5),
  X(0x4ed024fa), X(0x4edf81a5), X(0x4eeedcf3), X(0x4efe36e5),
  X(0x4f0d8f79), X(0x4f1ce6ad), X(0x4f2c3c82), X(0x4f3b90f4),
  X(0x4f4ae405), X(0x4f5a35b1), X(0x4f6985fa), X(0x4f78d4dc),
  X(0x4f882257), X(0x4f976e6a), X(0x4fa6b914), X(0x4fb60254),
  X(0x4fc54a28), X(0x4fd49090), X(0x4fe3d58b), X(0x4ff31917),
  X(0x50025b33), X(0x50119bde), X(0x5020db17), X(0x503018dd),
  X(0x503f552f), X(0x504e900b), X(0x505dc971), X(0x506d0160),
  X(0x507c37d7), X(0x508b6cd3), X(0x509aa055), X(0x50a9d25b),
  X(0x50b902e4), X(0x50c831ef), X(0x50d75f7b), X(0x50e68b87),
  X(0x50f5b612), X(0x5104df1a), X(0x5114069f), X(0x51232ca0),
  X(0x5132511a), X(0x5141740f), X(0x5150957b), X(0x515fb55f),
  X(0x516ed3b8), X(0x517df087), X(0x518d0bca), X(0x519c257f),
  X(0x51ab3da7), X(0x51ba543f), X(0x51c96947), X(0x51d87cbd),
  X(0x51e78ea1), X(0x51f69ef1), X(0x5205adad), X(0x5214bad3),
  X(0x5223c662), X(0x5232d05a), X(0x5241d8b9), X(0x5250df7d),
  X(0x525fe4a7), X(0x526ee835), X(0x527dea26), X(0x528cea78),
  X(0x529be92c), X(0x52aae63f), X(0x52b9e1b0), X(0x52c8db80),
  X(0x52d7d3ac), X(0x52e6ca33), X(0x52f5bf15), X(0x5304b251),
  X(0x5313a3e5), X(0x532293d0), X(0x53318212), X(0x53406ea8),
  X(0x534f5993), X(0x535e42d2), X(0x536d2a62), X(0x537c1043),
  X(0x538af475), X(0x5399d6f6), X(0x53a8b7c4), X(0x53b796e0),
  X(0x53c67447), X(0x53d54ffa), X(0x53e429f6), X(0x53f3023b),
  X(0x5401d8c8), X(0x5410ad9c), X(0x541f80b5), X(0x542e5213),
  X(0x543d21b5), X(0x544bef9a), X(0x545abbc0), X(0x54698627),
  X(0x54784ece), X(0x548715b3), X(0x5495dad6), X(0x54a49e35),
  X(0x54b35fd0), X(0x54c21fa6), X(0x54d0ddb5), X(0x54df99fd),
  X(0x54ee547c), X(0x54fd0d32), X(0x550bc41d), X(0x551a793d),
  X(0x55292c91), X(0x5537de16), X(0x55468dce), X(0x55553bb6),
  X(0x5563e7cd), X(0x55729213), X(0x55813a87), X(0x558fe127),
  X(0x559e85f2), X(0x55ad28e9), X(0x55bbca08), X(0x55ca6950),
  X(0x55d906c0), X(0x55e7a257), X(0x55f63c13), X(0x5604d3f4),
  X(0x561369f8), X(0x5621fe1f), X(0x56309067), X(0x563f20d1),
  X(0x564daf5a), X(0x565c3c02), X(0x566ac6c7), X(0x56794faa),
  X(0x5687d6a8), X(0x56965bc1), X(0x56a4def4), X(0x56b36040),
  X(0x56c1dfa4), X(0x56d05d1f), X(0x56ded8af), X(0x56ed5255),
  X(0x56fbca0f), X(0x570a3fdc), X(0x5718b3bc), X(0x572725ac),
  X(0x573595ad), X(0x574403bd), X(0x57526fdb), X(0x5760da07),
  X(0x576f423f), X(0x577da883), X(0x578c0cd1), X(0x579a6f29),
  X(0x57a8cf8a), X(0x57b72df2), X(0x57c58a61), X(0x57d3e4d6),
  X(0x57e23d50), X(0x57f093cd), X(0x57fee84e), X(0x580d3ad1),
  X(0x581b8b54), X(0x5829d9d8), X(0x5838265c), X(0x584670dd),
  X(0x5854b95c), X(0x5862ffd8), X(0x5871444f), X(0x587f86c1),
  X(0x588dc72c), X(0x589c0591), X(0x58aa41ed), X(0x58b87c40),
  X(0x58c6b489), X(0x58d4eac7), X(0x58e31ef9), X(0x58f1511f),
  X(0x58ff8137), X(0x590daf40), X(0x591bdb3a), X(0x592a0524),
  X(0x59382cfc), X(0x594652c2), X(0x59547675), X(0x59629815),
  X(0x5970b79f), X(0x597ed513), X(0x598cf071), X(0x599b09b7),
  X(0x59a920e5), X(0x59b735f9), X(0x59c548f4), X(0x59d359d2),
  X(0x59e16895), X(0x59ef753b), X(0x59fd7fc4), X(0x5a0b882d),
  X(0x5a198e77), X(0x5a2792a0), X(0x5a3594a9), X(0x5a43948e),
  X(0x5a519251), X(0x5a5f8df0), X(0x5a6d876a), X(0x5a7b7ebe),
  X(0x5a8973ec), X(0x5a9766f2), X(0x5aa557d0), X(0x5ab34685),
  X(0x5ac1330f), X(0x5acf1d6f), X(0x5add05a3), X(0x5aeaebaa),
  X(0x5af8cf84), X(0x5b06b12f), X(0x5b1490ab), X(0x5b226df7),
  X(0x5b304912), X(0x5b3e21fc), X(0x5b4bf8b2), X(0x5b59cd35),
  X(0x5b679f84), X(0x5b756f9e), X(0x5b833d82), X(0x5b91092e),
  X(0x5b9ed2a3), X(0x5bac99e0), X(0x5bba5ee3), X(0x5bc821ac),
  X(0x5bd5e23a), X(0x5be3a08c), X(0x5bf15ca1), X(0x5bff1679),
  X(0x5c0cce12), X(0x5c1a836c), X(0x5c283686), X(0x5c35e760),
  X(0x5c4395f7), X(0x5c51424c), X(0x5c5eec5e), X(0x5c6c942b),
  X(0x5c7a39b4), X(0x5c87dcf7), X(0x5c957df3), X(0x5ca31ca8),
  X(0x5cb0b915), X(0x5cbe5338), X(0x5ccbeb12), X(0x5cd980a1),
  X(0x5ce713e5), X(0x5cf4a4dd), X(0x5d023387), X(0x5d0fbfe4),
  X(0x5d1d49f2), X(0x5d2ad1b1), X(0x5d38571f), X(0x5d45da3c),
  X(0x5d535b08), X(0x5d60d981), X(0x5d6e55a7), X(0x5d7bcf78),
  X(0x5d8946f5), X(0x5d96bc1c), X(0x5da42eec), X(0x5db19f65),
  X(0x5dbf0d86), X(0x5dcc794e), X(0x5dd9e2bd), X(0x5de749d1),
  X(0x5df4ae8a), X(0x5e0210e7), X(0x5e0f70e7), X(0x5e1cce8a),
  X(0x5e2a29ce), X(0x5e3782b4), X(0x5e44d93a), X(0x5e522d5f),
  X(0x5e5f7f23), X(0x5e6cce85), X(0x5e7a1b85), X(0x5e876620),
  X(0x5e94ae58), X(0x5ea1f42a), X(0x5eaf3797), X(0x5ebc789d),
  X(0x5ec9b73c), X(0x5ed6f372), X(0x5ee42d41), X(0x5ef164a5),
  X(0x5efe999f), X(0x5f0bcc2f), X(0x5f18fc52), X(0x5f262a09),
  X(0x5f335553), X(0x5f407e2f), X(0x5f4da49d), X(0x5f5ac89b),
  X(0x5f67ea29), X(0x5f750946), X(0x5f8225f2), X(0x5f8f402b),
  X(0x5f9c57f2), X(0x5fa96d44), X(0x5fb68023), X(0x5fc3908c),
  X(0x5fd09e7f), X(0x5fdda9fc), X(0x5feab302), X(0x5ff7b990),
  X(0x6004bda5), X(0x6011bf40), X(0x601ebe62), X(0x602bbb09),
  X(0x6038b534), X(0x6045ace4), X(0x6052a216), X(0x605f94cb),
  X(0x606c8502), X(0x607972b9), X(0x60865df2), X(0x609346aa),
  X(0x60a02ce1), X(0x60ad1096), X(0x60b9f1c9), X(0x60c6d079),
  X(0x60d3aca5), X(0x60e0864d), X(0x60ed5d70), X(0x60fa320d),
  X(0x61070424), X(0x6113d3b4), X(0x6120a0bc), X(0x612d6b3c),
  X(0x613a3332), X(0x6146f89f), X(0x6153bb82), X(0x61607bd9),
  X(0x616d39a5), X(0x6179f4e5), X(0x6186ad98), X(0x619363bd),
  X(0x61a01753), X(0x61acc85b), X(0x61b976d3), X(0x61c622bc),
  X(0x61d2cc13), X(0x61df72d8), X(0x61ec170c), X(0x61f8b8ad),
  X(0x620557ba), X(0x6211f434), X(0x621e8e18), X(0x622b2568),
  X(0x6237ba21), X(0x62444c44), X(0x6250dbd0), X(0x625d68c4),
  X(0x6269f320), X(0x62767ae2), X(0x6283000b), X(0x628f829a),
  X(0x629c028e), X(0x62a87fe6), X(0x62b4faa2), X(0x62c172c2),
  X(0x62cde844), X(0x62da5b29), X(0x62e6cb6e), X(0x62f33915),
  X(0x62ffa41c), X(0x630c0c83), X(0x63187248), X(0x6324d56d),
  X(0x633135ef), X(0x633d93ce), X(0x6349ef0b), X(0x635647a3),
  X(0x63629d97), X(0x636ef0e6), X(0x637b418f), X(0x63878f92),
  X(0x6393daef), X(0x63a023a4), X(0x63ac69b1), X(0x63b8ad15),
  X(0x63c4edd1), X(0x63d12be3), X(0x63dd674b), X(0x63e9a008),
  X(0x63f5d61a), X(0x64020980), X(0x640e3a39), X(0x641a6846),
  X(0x642693a5), X(0x6432bc56), X(0x643ee258), X(0x644b05ab),
  X(0x6457264e), X(0x64634441), X(0x646f5f83), X(0x647b7814),
  X(0x64878df3), X(0x6493a120), X(0x649fb199), X(0x64abbf5f),
  X(0x64b7ca71), X(0x64c3d2ce), X(0x64cfd877), X(0x64dbdb69),
  X(0x64e7dba6), X(0x64f3d92b), X(0x64ffd3fa), X(0x650bcc11),
  X(0x6517c16f), X(0x6523b415), X(0x652fa402), X(0x653b9134),
  X(0x65477bad), X(0x6553636a), X(0x655f486d), X(0x656b2ab3),
  X(0x65770a3d), X(0x6582e70a), X(0x658ec11a), X(0x659a986d),
  X(0x65a66d00), X(0x65b23ed5), X(0x65be0deb), X(0x65c9da41),
  X(0x65d5a3d7), X(0x65e16aac), X(0x65ed2ebf), X(0x65f8f011),
  X(0x6604aea1), X(0x66106a6e), X(0x661c2377), X(0x6627d9be),
  X(0x66338d40), X(0x663f3dfd), X(0x664aebf5), X(0x66569728),
  X(0x66623f95), X(0x666de53b), X(0x6679881b), X(0x66852833),
  X(0x6690c583), X(0x669c600b), X(0x66a7f7ca), X(0x66b38cc0),
  X(0x66bf1eec), X(0x66caae4f), X(0x66d63ae6), X(0x66e1c4b3),
  X(0x66ed4bb4), X(0x66f8cfea), X(0x67045153), X(0x670fcfef),
  X(0x671b4bbe), X(0x6726c4bf), X(0x67323af3), X(0x673dae58),
  X(0x67491eee), X(0x67548cb5), X(0x675ff7ab), X(0x676b5fd2),
  X(0x6776c528), X(0x678227ad), X(0x678d8761), X(0x6798e443),
  X(0x67a43e52), X(0x67af958f), X(0x67bae9f9), X(0x67c63b8f),
  X(0x67d18a52), X(0x67dcd640), X(0x67e81f59), X(0x67f3659d),
  X(0x67fea90c), X(0x6809e9a5), X(0x68152768), X(0x68206254),
  X(0x682b9a68), X(0x6836cfa6), X(0x6842020b), X(0x684d3199),
  X(0x68585e4d), X(0x68638829), X(0x686eaf2b), X(0x6879d354),
  X(0x6884f4a2), X(0x68901316), X(0x689b2eb0), X(0x68a6476d),
  X(0x68b15d50), X(0x68bc7056), X(0x68c78080), X(0x68d28dcd),
  X(0x68dd983e), X(0x68e89fd0), X(0x68f3a486), X(0x68fea65d),
  X(0x6909a555), X(0x6914a16f), X(0x691f9aa9), X(0x692a9104),
  X(0x69358480), X(0x6940751b), X(0x694b62d5), X(0x69564daf),
  X(0x696135a7), X(0x696c1abe), X(0x6976fcf3), X(0x6981dc46),
  X(0x698cb8b6), X(0x69979243), X(0x69a268ed), X(0x69ad3cb4),
  X(0x69b80d97), X(0x69c2db96), X(0x69cda6b0), X(0x69d86ee5),
  X(0x69e33436), X(0x69edf6a1), X(0x69f8b626), X(0x6a0372c5),
  X(0x6a0e2c7e), X(0x6a18e350), X(0x6a23973c), X(0x6a2e4840),
  X(0x6a38f65d), X(0x6a43a191), X(0x6a4e49de), X(0x6a58ef42),
  X(0x6a6391be), X(0x6a6e3151), X(0x6a78cdfa), X(0x6a8367ba),
  X(0x6a8dfe90), X(0x6a98927c), X(0x6aa3237d), X(0x6aadb194),
  X(0x6ab83cc0), X(0x6ac2c500), X(0x6acd4a55), X(0x6ad7ccbf),
  X(0x6ae24c3c), X(0x6aecc8cd), X(0x6af74271), X(0x6b01b929),
  X(0x6b0c2cf4), X(0x6b169dd1), X(0x6b210bc1), X(0x6b2b76c2),
  X(0x6b35ded6), X(0x6b4043fc), X(0x6b4aa632), X(0x6b55057a),
  X(0x6b5f61d3), X(0x6b69bb3d), X(0x6b7411b7), X(0x6b7e6541),
  X(0x6b88b5db), X(0x6b930385), X(0x6b9d4e3f), X(0x6ba79607),
  X(0x6bb1dadf), X(0x6bbc1cc6), X(0x6bc65bbb), X(0x6bd097bf),
  X(0x6bdad0d0), X(0x6be506f0), X(0x6bef3a1d), X(0x6bf96a58),
  X(0x6c0397a0), X(0x6c0dc1f5), X(0x6c17e957), X(0x6c220dc6),
  X(0x6c2c2f41), X(0x6c364dc9), X(0x6c40695c), X(0x6c4a81fc),
  X(0x6c5497a7), X(0x6c5eaa5d), X(0x6c68ba1f), X(0x6c72c6eb),
  X(0x6c7cd0c3), X(0x6c86d7a6), X(0x6c90db92), X(0x6c9adc8a),
  X(0x6ca4da8b), X(0x6caed596), X(0x6cb8cdab), X(0x6cc2c2ca),
  X(0x6cccb4f2), X(0x6cd6a424), X(0x6ce0905e), X(0x6cea79a1),
  X(0x6cf45fee), X(0x6cfe4342), X(0x6d0823a0), X(0x6d120105),
  X(0x6d1bdb73), X(0x6d25b2e8), X(0x6d2f8765), X(0x6d3958ea),
  X(0x6d432777), X(0x6d4cf30a), X(0x6d56bba5), X(0x6d608147),
  X(0x6d6a43f0), X(0x6d7403a0), X(0x6d7dc056), X(0x6d877a13),
  X(0x6d9130d6), X(0x6d9ae4a0), X(0x6da4956f), X(0x6dae4345),
  X(0x6db7ee20), X(0x6dc19601), X(0x6dcb3ae7), X(0x6dd4dcd3),
  X(0x6dde7bc4), X(0x6de817bb), X(0x6df1b0b6), X(0x6dfb46b7),
  X(0x6e04d9bc), X(0x6e0e69c7), X(0x6e17f6d5), X(0x6e2180e9),
  X(0x6e2b0801), X(0x6e348c1d), X(0x6e3e0d3d), X(0x6e478b62),
  X(0x6e51068a), X(0x6e5a7eb7), X(0x6e63f3e7), X(0x6e6d661b),
  X(0x6e76d552), X(0x6e80418e), X(0x6e89aacc), X(0x6e93110f),
  X(0x6e9c7454), X(0x6ea5d49d), X(0x6eaf31e9), X(0x6eb88c37),
  X(0x6ec1e389), X(0x6ecb37de), X(0x6ed48936), X(0x6eddd790),
  X(0x6ee722ee), X(0x6ef06b4d), X(0x6ef9b0b0), X(0x6f02f315),
  X(0x6f0c327c), X(0x6f156ee6), X(0x6f1ea852), X(0x6f27dec1),
  X(0x6f311232), X(0x6f3a42a5), X(0x6f43701a), X(0x6f4c9a91),
  X(0x6f55c20a), X(0x6f5ee686), X(0x6f680803), X(0x6f712682),
  X(0x6f7a4203), X(0x6f835a86), X(0x6f8c700b), X(0x6f958291),
  X(0x6f9e921a), X(0x6fa79ea4), X(0x6fb0a830), X(0x6fb9aebd),
  X(0x6fc2b24c), X(0x6fcbb2dd), X(0x6fd4b06f), X(0x6fddab03),
  X(0x6fe6a299), X(0x6fef9730), X(0x6ff888c9), X(0x70017763),
  X(0x700a62ff), X(0x70134b9c), X(0x701c313b), X(0x702513dc),
  X(0x702df37e), X(0x7036d021), X(0x703fa9c6), X(0x7048806d),
  X(0x70515415), X(0x705a24bf), X(0x7062f26b), X(0x706bbd17),
  X(0x707484c6), X(0x707d4976), X(0x70860b28), X(0x708ec9dc),
  X(0x70978591), X(0x70a03e48), X(0x70a8f400), X(0x70b1a6bb),
  X(0x70ba5677), X(0x70c30335), X(0x70cbacf5), X(0x70d453b6),
  X(0x70dcf77a), X(0x70e59840), X(0x70ee3607), X(0x70f6d0d1),
  X(0x70ff689d), X(0x7107fd6b), X(0x71108f3b), X(0x71191e0d),
  X(0x7121a9e2), X(0x712a32b9), X(0x7132b892), X(0x713b3b6e),
  X(0x7143bb4c), X(0x714c382d), X(0x7154b211), X(0x715d28f7),
  X(0x71659ce0), X(0x716e0dcc), X(0x71767bbb), X(0x717ee6ac),
  X(0x71874ea1), X(0x718fb399), X(0x71981594), X(0x71a07493),
  X(0x71a8d094), X(0x71b1299a), X(0x71b97fa2), X(0x71c1d2af),
  X(0x71ca22bf), X(0x71d26fd2), X(0x71dab9ea), X(0x71e30106),
  X(0x71eb4526), X(0x71f3864a), X(0x71fbc472), X(0x7203ff9e),
  X(0x720c37cf), X(0x72146d05), X(0x721c9f3f), X(0x7224ce7e),
  X(0x722cfac2), X(0x7235240b), X(0x723d4a59), X(0x72456dad),
  X(0x724d8e05), X(0x7255ab63), X(0x725dc5c7), X(0x7265dd31),
  X(0x726df1a0), X(0x72760315), X(0x727e1191), X(0x72861d12),
  X(0x728e259a), X(0x72962b28), X(0x729e2dbd), X(0x72a62d59),
  X(0x72ae29fc), X(0x72b623a5), X(0x72be1a56), X(0x72c60e0e),
  X(0x72cdfece), X(0x72d5ec95), X(0x72ddd764), X(0x72e5bf3b),
  X(0x72eda41a), X(0x72f58601), X(0x72fd64f1), X(0x730540e9),
  X(0x730d19e9), X(0x7314eff3), X(0x731cc305), X(0x73249321),
  X(0x732c6046), X(0x73342a75), X(0x733bf1ad), X(0x7343b5ef),
  X(0x734b773b), X(0x73533591), X(0x735af0f2), X(0x7362a95d),
  X(0x736a5ed3), X(0x73721153), X(0x7379c0df), X(0x73816d76),
  X(0x73891719), X(0x7390bdc7), X(0x73986181), X(0x73a00247),
  X(0x73a7a01a), X(0x73af3af8), X(0x73b6d2e4), X(0x73be67dc),
  X(0x73c5f9e1), X(0x73cd88f3), X(0x73d51513), X(0x73dc9e40),
  X(0x73e4247c), X(0x73eba7c5), X(0x73f3281c), X(0x73faa582),
  X(0x74021ff7), X(0x7409977b), X(0x74110c0d), X(0x74187daf),
  X(0x741fec61), X(0x74275822), X(0x742ec0f3), X(0x743626d5),
  X(0x743d89c7), X(0x7444e9c9), X(0x744c46dd), X(0x7453a101),
  X(0x745af837), X(0x74624c7f), X(0x74699dd8), X(0x7470ec44),
  X(0x747837c2), X(0x747f8052), X(0x7486c5f5), X(0x748e08ac),
  X(0x74954875), X(0x749c8552), X(0x74a3bf43), X(0x74aaf648),
  X(0x74b22a62), X(0x74b95b90), X(0x74c089d2), X(0x74c7b52a),
  X(0x74cedd97), X(0x74d6031a), X(0x74dd25b2), X(0x74e44561),
  X(0x74eb6226), X(0x74f27c02), X(0x74f992f5), X(0x7500a6ff),
  X(0x7507b820), X(0x750ec659), X(0x7515d1aa), X(0x751cda14),
  X(0x7523df96), X(0x752ae231), X(0x7531e1e5), X(0x7538deb2),
  X(0x753fd89a), X(0x7546cf9b), X(0x754dc3b7), X(0x7554b4ed),
  X(0x755ba33e), X(0x75628eaa), X(0x75697732), X(0x75705cd5),
  X(0x75773f95), X(0x757e1f71), X(0x7584fc6a), X(0x758bd67f),
  X(0x7592adb2), X(0x75998203), X(0x75a05371), X(0x75a721fe),
  X(0x75adeda9), X(0x75b4b673), X(0x75bb7c5c), X(0x75c23f65),
  X(0x75c8ff8d), X(0x75cfbcd6), X(0x75d6773f), X(0x75dd2ec8),
  X(0x75e3e373), X(0x75ea953f), X(0x75f1442d), X(0x75f7f03d),
  X(0x75fe996f), X(0x76053fc5), X(0x760be33d), X(0x761283d8),
  X(0x76192197), X(0x761fbc7b), X(0x76265482), X(0x762ce9af),
  X(0x76337c01), X(0x763a0b78), X(0x76409814), X(0x764721d7),
  X(0x764da8c1), X(0x76542cd1), X(0x765aae08), X(0x76612c67),
  X(0x7667a7ee), X(0x766e209d), X(0x76749675), X(0x767b0975),
  X(0x7681799f), X(0x7687e6f3), X(0x768e5170), X(0x7694b918),
  X(0x769b1deb), X(0x76a17fe9), X(0x76a7df13), X(0x76ae3b68),
  X(0x76b494ea), X(0x76baeb98), X(0x76c13f74), X(0x76c7907c),
  X(0x76cddeb3), X(0x76d42a18), X(0x76da72ab), X(0x76e0b86d),
  X(0x76e6fb5e), X(0x76ed3b7f), X(0x76f378d0), X(0x76f9b352),
  X(0x76ffeb05), X(0x77061fe8), X(0x770c51fe), X(0x77128145),
  X(0x7718adbf), X(0x771ed76c), X(0x7724fe4c), X(0x772b225f),
  X(0x773143a7), X(0x77376223), X(0x773d7dd3), X(0x774396ba),
  X(0x7749acd5), X(0x774fc027), X(0x7755d0af), X(0x775bde6f),
  X(0x7761e965), X(0x7767f193), X(0x776df6fa), X(0x7773f998),
  X(0x7779f970), X(0x777ff681), X(0x7785f0cd), X(0x778be852),
  X(0x7791dd12), X(0x7797cf0d), X(0x779dbe43), X(0x77a3aab6),
  X(0x77a99465), X(0x77af7b50), X(0x77b55f79), X(0x77bb40e0),
  X(0x77c11f85), X(0x77c6fb68), X(0x77ccd48a), X(0x77d2aaec),
  X(0x77d87e8d), X(0x77de4f6f), X(0x77e41d92), X(0x77e9e8f5),
  X(0x77efb19b), X(0x77f57782), X(0x77fb3aad), X(0x7800fb1a),
  X(0x7806b8ca), X(0x780c73bf), X(0x78122bf7), X(0x7817e175),
  X(0x781d9438), X(0x78234440), X(0x7828f18f), X(0x782e9c25),
  X(0x78344401), X(0x7839e925), X(0x783f8b92), X(0x78452b46),
  X(0x784ac844), X(0x7850628b), X(0x7855fa1c), X(0x785b8ef8),
  X(0x7861211e), X(0x7866b090), X(0x786c3d4d), X(0x7871c757),
  X(0x78774ead), X(0x787cd351), X(0x78825543), X(0x7887d483),
  X(0x788d5111), X(0x7892caef), X(0x7898421c), X(0x789db69a),
  X(0x78a32868), X(0x78a89787), X(0x78ae03f8), X(0x78b36dbb),
  X(0x78b8d4d1), X(0x78be393a), X(0x78c39af6), X(0x78c8fa06),
  X(0x78ce566c), X(0x78d3b026), X(0x78d90736), X(0x78de5b9c),
  X(0x78e3ad58), X(0x78e8fc6c), X(0x78ee48d7), X(0x78f3929b),
  X(0x78f8d9b7), X(0x78fe1e2c), X(0x79035ffb), X(0x79089f24),
  X(0x790ddba8), X(0x79131587), X(0x79184cc2), X(0x791d8159),
  X(0x7922b34d), X(0x7927e29e), X(0x792d0f4d), X(0x7932395a),
  X(0x793760c6), X(0x793c8591), X(0x7941a7bd), X(0x7946c749),
  X(0x794be435), X(0x7950fe84), X(0x79561634), X(0x795b2b47),
  X(0x79603dbc), X(0x79654d96), X(0x796a5ad4), X(0x796f6576),
  X(0x79746d7e), X(0x797972eb), X(0x797e75bf), X(0x798375f9),
  X(0x7988739b), X(0x798d6ea5), X(0x79926717), X(0x79975cf2),
  X(0x799c5037), X(0x79a140e6), X(0x79a62f00), X(0x79ab1a85),
  X(0x79b00376), X(0x79b4e9d3), X(0x79b9cd9d), X(0x79beaed4),
  X(0x79c38d79), X(0x79c8698d), X(0x79cd4310), X(0x79d21a03),
  X(0x79d6ee66), X(0x79dbc03a), X(0x79e08f7f), X(0x79e55c36),
  X(0x79ea265f), X(0x79eeedfc), X(0x79f3b30c), X(0x79f87590),
  X(0x79fd3589), X(0x7a01f2f7), X(0x7a06addc), X(0x7a0b6636),
  X(0x7a101c08), X(0x7a14cf52), X(0x7a198013), X(0x7a1e2e4d),
  X(0x7a22da01), X(0x7a27832f), X(0x7a2c29d7), X(0x7a30cdfa),
  X(0x7a356f99), X(0x7a3a0eb4), X(0x7a3eab4c), X(0x7a434561),
  X(0x7a47dcf5), X(0x7a4c7207), X(0x7a510498), X(0x7a5594a9),
  X(0x7a5a223a), X(0x7a5ead4d), X(0x7a6335e0), X(0x7a67bbf6),
  X(0x7a6c3f8f), X(0x7a70c0ab), X(0x7a753f4b), X(0x7a79bb6f),
  X(0x7a7e3519), X(0x7a82ac48), X(0x7a8720fe), X(0x7a8b933b),
  X(0x7a9002ff), X(0x7a94704b), X(0x7a98db20), X(0x7a9d437e),
  X(0x7aa1a967), X(0x7aa60cd9), X(0x7aaa6dd7), X(0x7aaecc61),
  X(0x7ab32877), X(0x7ab7821b), X(0x7abbd94b), X(0x7ac02e0a),
  X(0x7ac48058), X(0x7ac8d035), X(0x7acd1da3), X(0x7ad168a1),
  X(0x7ad5b130), X(0x7ad9f751), X(0x7ade3b05), X(0x7ae27c4c),
  X(0x7ae6bb27), X(0x7aeaf796), X(0x7aef319a), X(0x7af36934),
  X(0x7af79e64), X(0x7afbd12c), X(0x7b00018a), X(0x7b042f81),
  X(0x7b085b10), X(0x7b0c8439), X(0x7b10aafc), X(0x7b14cf5a),
  X(0x7b18f153), X(0x7b1d10e8), X(0x7b212e1a), X(0x7b2548e9),
  X(0x7b296155), X(0x7b2d7761), X(0x7b318b0b), X(0x7b359c55),
  X(0x7b39ab3f), X(0x7b3db7cb), X(0x7b41c1f8), X(0x7b45c9c8),
  X(0x7b49cf3b), X(0x7b4dd251), X(0x7b51d30b), X(0x7b55d16b),
  X(0x7b59cd70), X(0x7b5dc71b), X(0x7b61be6d), X(0x7b65b366),
  X(0x7b69a608), X(0x7b6d9653), X(0x7b718447), X(0x7b756fe5),
  X(0x7b79592e), X(0x7b7d4022), X(0x7b8124c3), X(0x7b850710),
  X(0x7b88e70a), X(0x7b8cc4b3), X(0x7b90a00a), X(0x7b947911),
  X(0x7b984fc8), X(0x7b9c242f), X(0x7b9ff648), X(0x7ba3c612),
  X(0x7ba79390), X(0x7bab5ec1), X(0x7baf27a5), X(0x7bb2ee3f),
  X(0x7bb6b28e), X(0x7bba7493), X(0x7bbe344e), X(0x7bc1f1c1),
  X(0x7bc5acec), X(0x7bc965cf), X(0x7bcd1c6c), X(0x7bd0d0c3),
  X(0x7bd482d4), X(0x7bd832a1), X(0x7bdbe02a), X(0x7bdf8b70),
  X(0x7be33473), X(0x7be6db34), X(0x7bea7fb4), X(0x7bee21f4),
  X(0x7bf1c1f3), X(0x7bf55fb3), X(0x7bf8fb35), X(0x7bfc9479),
  X(0x7c002b7f), X(0x7c03c04a), X(0x7c0752d8), X(0x7c0ae32b),
  X(0x7c0e7144), X(0x7c11fd23), X(0x7c1586c9), X(0x7c190e36),
  X(0x7c1c936c), X(0x7c20166b), X(0x7c239733), X(0x7c2715c6),
  X(0x7c2a9224), X(0x7c2e0c4e), X(0x7c318444), X(0x7c34fa07),
  X(0x7c386d98), X(0x7c3bdef8), X(0x7c3f4e26), X(0x7c42bb25),
  X(0x7c4625f4), X(0x7c498e95), X(0x7c4cf507), X(0x7c50594c),
  X(0x7c53bb65), X(0x7c571b51), X(0x7c5a7913), X(0x7c5dd4aa),
  X(0x7c612e17), X(0x7c64855b), X(0x7c67da76), X(0x7c6b2d6a),
  X(0x7c6e7e37), X(0x7c71ccdd), X(0x7c75195e), X(0x7c7863ba),
  X(0x7c7babf1), X(0x7c7ef206), X(0x7c8235f7), X(0x7c8577c6),
  X(0x7c88b774), X(0x7c8bf502), X(0x7c8f306f), X(0x7c9269bd),
  X(0x7c95a0ec), X(0x7c98d5fe), X(0x7c9c08f2), X(0x7c9f39cb),
  X(0x7ca26887), X(0x7ca59528), X(0x7ca8bfb0), X(0x7cabe81d),
  X(0x7caf0e72), X(0x7cb232af), X(0x7cb554d4), X(0x7cb874e2),
  X(0x7cbb92db), X(0x7cbeaebe), X(0x7cc1c88d), X(0x7cc4e047),
  X(0x7cc7f5ef), X(0x7ccb0984), X(0x7cce1b08), X(0x7cd12a7b),
  X(0x7cd437dd), X(0x7cd74330), X(0x7cda4c74), X(0x7cdd53aa),
  X(0x7ce058d3), X(0x7ce35bef), X(0x7ce65cff), X(0x7ce95c04),
  X(0x7cec58ff), X(0x7cef53f0), X(0x7cf24cd7), X(0x7cf543b7),
  X(0x7cf8388f), X(0x7cfb2b60), X(0x7cfe1c2b), X(0x7d010af1),
  X(0x7d03f7b2), X(0x7d06e26f), X(0x7d09cb29), X(0x7d0cb1e0),
  X(0x7d0f9696), X(0x7d12794b), X(0x7d1559ff), X(0x7d1838b4),
  X(0x7d1b156a), X(0x7d1df022), X(0x7d20c8dd), X(0x7d239f9b),
  X(0x7d26745e), X(0x7d294725), X(0x7d2c17f1), X(0x7d2ee6c4),
  X(0x7d31b39f), X(0x7d347e81), X(0x7d37476b), X(0x7d3a0e5f),
  X(0x7d3cd35d), X(0x7d3f9665), X(0x7d425779), X(0x7d451699),
  X(0x7d47d3c6), X(0x7d4a8f01), X(0x7d4d484b), X(0x7d4fffa3),
  X(0x7d52b50c), X(0x7d556885), X(0x7d581a0f), X(0x7d5ac9ac),
  X(0x7d5d775c), X(0x7d60231f), X(0x7d62ccf6), X(0x7d6574e3),
  X(0x7d681ae6), X(0x7d6abeff), X(0x7d6d612f), X(0x7d700178),
  X(0x7d729fd9), X(0x7d753c54), X(0x7d77d6e9), X(0x7d7a6f9a),
  X(0x7d7d0666), X(0x7d7f9b4f), X(0x7d822e55), X(0x7d84bf79),
  X(0x7d874ebc), X(0x7d89dc1e), X(0x7d8c67a1), X(0x7d8ef144),
  X(0x7d91790a), X(0x7d93fef2), X(0x7d9682fd), X(0x7d99052d),
  X(0x7d9b8581), X(0x7d9e03fb), X(0x7da0809b), X(0x7da2fb62),
  X(0x7da57451), X(0x7da7eb68), X(0x7daa60a8), X(0x7dacd413),
  X(0x7daf45a9), X(0x7db1b56a), X(0x7db42357), X(0x7db68f71),
  X(0x7db8f9b9), X(0x7dbb6230), X(0x7dbdc8d6), X(0x7dc02dac),
  X(0x7dc290b3), X(0x7dc4f1eb), X(0x7dc75156), X(0x7dc9aef4),
  X(0x7dcc0ac5), X(0x7dce64cc), X(0x7dd0bd07), X(0x7dd31379),
  X(0x7dd56821), X(0x7dd7bb01), X(0x7dda0c1a), X(0x7ddc5b6b),
  X(0x7ddea8f7), X(0x7de0f4bd), X(0x7de33ebe), X(0x7de586fc),
  X(0x7de7cd76), X(0x7dea122e), X(0x7dec5525), X(0x7dee965a),
  X(0x7df0d5d0), X(0x7df31386), X(0x7df54f7e), X(0x7df789b8),
  X(0x7df9c235), X(0x7dfbf8f5), X(0x7dfe2dfa), X(0x7e006145),
  X(0x7e0292d5), X(0x7e04c2ac), X(0x7e06f0cb), X(0x7e091d32),
  X(0x7e0b47e1), X(0x7e0d70db), X(0x7e0f981f), X(0x7e11bdaf),
  X(0x7e13e18a), X(0x7e1603b3), X(0x7e182429), X(0x7e1a42ed),
  X(0x7e1c6001), X(0x7e1e7b64), X(0x7e209518), X(0x7e22ad1d),
  X(0x7e24c375), X(0x7e26d81f), X(0x7e28eb1d), X(0x7e2afc70),
  X(0x7e2d0c17), X(0x7e2f1a15), X(0x7e31266a), X(0x7e333115),
  X(0x7e353a1a), X(0x7e374177), X(0x7e39472e), X(0x7e3b4b3f),
  X(0x7e3d4dac), X(0x7e3f4e75), X(0x7e414d9a), X(0x7e434b1e),
  X(0x7e4546ff), X(0x7e474140), X(0x7e4939e0), X(0x7e4b30e2),
  X(0x7e4d2644), X(0x7e4f1a09), X(0x7e510c30), X(0x7e52fcbc),
  X(0x7e54ebab), X(0x7e56d900), X(0x7e58c4bb), X(0x7e5aaedd),
  X(0x7e5c9766), X(0x7e5e7e57), X(0x7e6063b2), X(0x7e624776),
  X(0x7e6429a5), X(0x7e660a3f), X(0x7e67e945), X(0x7e69c6b8),
  X(0x7e6ba299), X(0x7e6d7ce7), X(0x7e6f55a5), X(0x7e712cd3),
  X(0x7e730272), X(0x7e74d682), X(0x7e76a904), X(0x7e7879f9),
  X(0x7e7a4962), X(0x7e7c173f), X(0x7e7de392), X(0x7e7fae5a),
  X(0x7e817799), X(0x7e833f50), X(0x7e85057f), X(0x7e86ca27),
  X(0x7e888d49), X(0x7e8a4ee5), X(0x7e8c0efd), X(0x7e8dcd91),
  X(0x7e8f8aa1), X(0x7e914630), X(0x7e93003c), X(0x7e94b8c8),
  X(0x7e966fd4), X(0x7e982560), X(0x7e99d96e), X(0x7e9b8bfe),
  X(0x7e9d3d10), X(0x7e9eeca7), X(0x7ea09ac2), X(0x7ea24762),
  X(0x7ea3f288), X(0x7ea59c35), X(0x7ea7446a), X(0x7ea8eb27),
  X(0x7eaa906c), X(0x7eac343c), X(0x7eadd696), X(0x7eaf777b),
  X(0x7eb116ed), X(0x7eb2b4eb), X(0x7eb45177), X(0x7eb5ec91),
  X(0x7eb7863b), X(0x7eb91e74), X(0x7ebab53e), X(0x7ebc4a99),
  X(0x7ebdde87), X(0x7ebf7107), X(0x7ec1021b), X(0x7ec291c3),
  X(0x7ec42001), X(0x7ec5acd5), X(0x7ec7383f), X(0x7ec8c241),
  X(0x7eca4adb), X(0x7ecbd20d), X(0x7ecd57da), X(0x7ecedc41),
  X(0x7ed05f44), X(0x7ed1e0e2), X(0x7ed3611d), X(0x7ed4dff6),
  X(0x7ed65d6d), X(0x7ed7d983), X(0x7ed95438), X(0x7edacd8f),
  X(0x7edc4586), X(0x7eddbc20), X(0x7edf315c), X(0x7ee0a53c),
  X(0x7ee217c1), X(0x7ee388ea), X(0x7ee4f8b9), X(0x7ee6672f),
  X(0x7ee7d44c), X(0x7ee94012), X(0x7eeaaa80), X(0x7eec1397),
  X(0x7eed7b59), X(0x7eeee1c6), X(0x7ef046df), X(0x7ef1aaa5),
  X(0x7ef30d18), X(0x7ef46e39), X(0x7ef5ce09), X(0x7ef72c88),
  X(0x7ef889b8), X(0x7ef9e599), X(0x7efb402c), X(0x7efc9972),
  X(0x7efdf16b), X(0x7eff4818), X(0x7f009d79), X(0x7f01f191),
  X(0x7f03445f), X(0x7f0495e4), X(0x7f05e620), X(0x7f073516),
  X(0x7f0882c5), X(0x7f09cf2d), X(0x7f0b1a51), X(0x7f0c6430),
  X(0x7f0daccc), X(0x7f0ef425), X(0x7f103a3b), X(0x7f117f11),
  X(0x7f12c2a5), X(0x7f1404fa), X(0x7f15460f), X(0x7f1685e6),
  X(0x7f17c47f), X(0x7f1901db), X(0x7f1a3dfb), X(0x7f1b78e0),
  X(0x7f1cb28a), X(0x7f1deafa), X(0x7f1f2231), X(0x7f20582f),
  X(0x7f218cf5), X(0x7f22c085), X(0x7f23f2de), X(0x7f252401),
  X(0x7f2653f0), X(0x7f2782ab), X(0x7f28b032), X(0x7f29dc87),
  X(0x7f2b07aa), X(0x7f2c319c), X(0x7f2d5a5e), X(0x7f2e81f0),
  X(0x7f2fa853), X(0x7f30cd88), X(0x7f31f18f), X(0x7f33146a),
  X(0x7f343619), X(0x7f35569c), X(0x7f3675f6), X(0x7f379425),
  X(0x7f38b12c), X(0x7f39cd0a), X(0x7f3ae7c0), X(0x7f3c0150),
  X(0x7f3d19ba), X(0x7f3e30fe), X(0x7f3f471e), X(0x7f405c1a),
  X(0x7f416ff3), X(0x7f4282a9), X(0x7f43943e), X(0x7f44a4b2),
  X(0x7f45b405), X(0x7f46c239), X(0x7f47cf4e), X(0x7f48db45),
  X(0x7f49e61f), X(0x7f4aefdc), X(0x7f4bf87e), X(0x7f4d0004),
  X(0x7f4e0670), X(0x7f4f0bc2), X(0x7f500ffb), X(0x7f51131c),
  X(0x7f521525), X(0x7f531618), X(0x7f5415f4), X(0x7f5514bb),
  X(0x7f56126e), X(0x7f570f0c), X(0x7f580a98), X(0x7f590511),
  X(0x7f59fe78), X(0x7f5af6ce), X(0x7f5bee14), X(0x7f5ce44a),
  X(0x7f5dd972), X(0x7f5ecd8b), X(0x7f5fc097), X(0x7f60b296),
  X(0x7f61a389), X(0x7f629370), X(0x7f63824e), X(0x7f647021),
  X(0x7f655ceb), X(0x7f6648ad), X(0x7f673367), X(0x7f681d19),
  X(0x7f6905c6), X(0x7f69ed6d), X(0x7f6ad40f), X(0x7f6bb9ad),
  X(0x7f6c9e48), X(0x7f6d81e0), X(0x7f6e6475), X(0x7f6f460a),
  X(0x7f70269d), X(0x7f710631), X(0x7f71e4c6), X(0x7f72c25c),
  X(0x7f739ef4), X(0x7f747a8f), X(0x7f75552e), X(0x7f762ed1),
  X(0x7f770779), X(0x7f77df27), X(0x7f78b5db), X(0x7f798b97),
  X(0x7f7a605a), X(0x7f7b3425), X(0x7f7c06fa), X(0x7f7cd8d9),
  X(0x7f7da9c2), X(0x7f7e79b7), X(0x7f7f48b8), X(0x7f8016c5),
  X(0x7f80e3e0), X(0x7f81b009), X(0x7f827b40), X(0x7f834588),
  X(0x7f840edf), X(0x7f84d747), X(0x7f859ec1), X(0x7f86654d),
  X(0x7f872aec), X(0x7f87ef9e), X(0x7f88b365), X(0x7f897641),
  X(0x7f8a3832), X(0x7f8af93a), X(0x7f8bb959), X(0x7f8c7890),
  X(0x7f8d36df), X(0x7f8df448), X(0x7f8eb0ca), X(0x7f8f6c67),
  X(0x7f90271e), X(0x7f90e0f2), X(0x7f9199e2), X(0x7f9251f0),
  X(0x7f93091b), X(0x7f93bf65), X(0x7f9474ce), X(0x7f952958),
  X(0x7f95dd01), X(0x7f968fcd), X(0x7f9741ba), X(0x7f97f2ca),
  X(0x7f98a2fd), X(0x7f995254), X(0x7f9a00d0), X(0x7f9aae71),
  X(0x7f9b5b38), X(0x7f9c0726), X(0x7f9cb23b), X(0x7f9d5c78),
  X(0x7f9e05de), X(0x7f9eae6e), X(0x7f9f5627), X(0x7f9ffd0b),
  X(0x7fa0a31b), X(0x7fa14856), X(0x7fa1ecbf), X(0x7fa29054),
  X(0x7fa33318), X(0x7fa3d50b), X(0x7fa4762c), X(0x7fa5167e),
  X(0x7fa5b601), X(0x7fa654b5), X(0x7fa6f29b), X(0x7fa78fb3),
  X(0x7fa82bff), X(0x7fa8c77f), X(0x7fa96234), X(0x7fa9fc1e),
  X(0x7faa953e), X(0x7fab2d94), X(0x7fabc522), X(0x7fac5be8),
  X(0x7facf1e6), X(0x7fad871d), X(0x7fae1b8f), X(0x7faeaf3b),
  X(0x7faf4222), X(0x7fafd445), X(0x7fb065a4), X(0x7fb0f641),
  X(0x7fb1861b), X(0x7fb21534), X(0x7fb2a38c), X(0x7fb33124),
  X(0x7fb3bdfb), X(0x7fb44a14), X(0x7fb4d56f), X(0x7fb5600c),
  X(0x7fb5e9ec), X(0x7fb6730f), X(0x7fb6fb76), X(0x7fb78323),
  X(0x7fb80a15), X(0x7fb8904d), X(0x7fb915cc), X(0x7fb99a92),
  X(0x7fba1ea0), X(0x7fbaa1f7), X(0x7fbb2497), X(0x7fbba681),
  X(0x7fbc27b5), X(0x7fbca835), X(0x7fbd2801), X(0x7fbda719),
  X(0x7fbe257e), X(0x7fbea331), X(0x7fbf2032), X(0x7fbf9c82),
  X(0x7fc01821), X(0x7fc09311), X(0x7fc10d52), X(0x7fc186e4),
  X(0x7fc1ffc8), X(0x7fc277ff), X(0x7fc2ef89), X(0x7fc36667),
  X(0x7fc3dc9a), X(0x7fc45221), X(0x7fc4c6ff), X(0x7fc53b33),
  X(0x7fc5aebe), X(0x7fc621a0), X(0x7fc693db), X(0x7fc7056f),
  X(0x7fc7765c), X(0x7fc7e6a3), X(0x7fc85645), X(0x7fc8c542),
  X(0x7fc9339b), X(0x7fc9a150), X(0x7fca0e63), X(0x7fca7ad3),
  X(0x7fcae6a2), X(0x7fcb51cf), X(0x7fcbbc5c), X(0x7fcc2649),
  X(0x7fcc8f97), X(0x7fccf846), X(0x7fcd6058), X(0x7fcdc7cb),
  X(0x7fce2ea2), X(0x7fce94dd), X(0x7fcefa7b), X(0x7fcf5f7f),
  X(0x7fcfc3e8), X(0x7fd027b7), X(0x7fd08aed), X(0x7fd0ed8b),
  X(0x7fd14f90), X(0x7fd1b0fd), X(0x7fd211d4), X(0x7fd27214),
  X(0x7fd2d1bf), X(0x7fd330d4), X(0x7fd38f55), X(0x7fd3ed41),
  X(0x7fd44a9a), X(0x7fd4a761), X(0x7fd50395), X(0x7fd55f37),
  X(0x7fd5ba48), X(0x7fd614c9), X(0x7fd66eba), X(0x7fd6c81b),
  X(0x7fd720ed), X(0x7fd77932), X(0x7fd7d0e8), X(0x7fd82812),
  X(0x7fd87eae), X(0x7fd8d4bf), X(0x7fd92a45), X(0x7fd97f40),
  X(0x7fd9d3b0), X(0x7fda2797), X(0x7fda7af5), X(0x7fdacdca),
  X(0x7fdb2018), X(0x7fdb71dd), X(0x7fdbc31c), X(0x7fdc13d5),
  X(0x7fdc6408), X(0x7fdcb3b6), X(0x7fdd02df), X(0x7fdd5184),
  X(0x7fdd9fa5), X(0x7fdded44), X(0x7fde3a60), X(0x7fde86fb),
  X(0x7fded314), X(0x7fdf1eac), X(0x7fdf69c4), X(0x7fdfb45d),
  X(0x7fdffe76), X(0x7fe04811), X(0x7fe0912e), X(0x7fe0d9ce),
  X(0x7fe121f0), X(0x7fe16996), X(0x7fe1b0c1), X(0x7fe1f770),
  X(0x7fe23da4), X(0x7fe2835f), X(0x7fe2c89f), X(0x7fe30d67),
  X(0x7fe351b5), X(0x7fe3958c), X(0x7fe3d8ec), X(0x7fe41bd4),
  X(0x7fe45e46), X(0x7fe4a042), X(0x7fe4e1c8), X(0x7fe522da),
  X(0x7fe56378), X(0x7fe5a3a1), X(0x7fe5e358), X(0x7fe6229b),
  X(0x7fe6616d), X(0x7fe69fcc), X(0x7fe6ddbb), X(0x7fe71b39),
  X(0x7fe75847), X(0x7fe794e5), X(0x7fe7d114), X(0x7fe80cd5),
  X(0x7fe84827), X(0x7fe8830c), X(0x7fe8bd84), X(0x7fe8f78f),
  X(0x7fe9312f), X(0x7fe96a62), X(0x7fe9a32b), X(0x7fe9db8a),
  X(0x7fea137e), X(0x7fea4b09), X(0x7fea822b), X(0x7feab8e5),
  X(0x7feaef37), X(0x7feb2521), X(0x7feb5aa4), X(0x7feb8fc1),
  X(0x7febc478), X(0x7febf8ca), X(0x7fec2cb6), X(0x7fec603e),
  X(0x7fec9363), X(0x7fecc623), X(0x7fecf881), X(0x7fed2a7c),
  X(0x7fed5c16), X(0x7fed8d4e), X(0x7fedbe24), X(0x7fedee9b),
  X(0x7fee1eb1), X(0x7fee4e68), X(0x7fee7dc0), X(0x7feeacb9),
  X(0x7feedb54), X(0x7fef0991), X(0x7fef3771), X(0x7fef64f5),
  X(0x7fef921d), X(0x7fefbee8), X(0x7fefeb59), X(0x7ff0176f),
  X(0x7ff0432a), X(0x7ff06e8c), X(0x7ff09995), X(0x7ff0c444),
  X(0x7ff0ee9c), X(0x7ff1189b), X(0x7ff14243), X(0x7ff16b94),
  X(0x7ff1948e), X(0x7ff1bd32), X(0x7ff1e581), X(0x7ff20d7b),
  X(0x7ff2351f), X(0x7ff25c70), X(0x7ff2836d), X(0x7ff2aa17),
  X(0x7ff2d06d), X(0x7ff2f672), X(0x7ff31c24), X(0x7ff34185),
  X(0x7ff36695), X(0x7ff38b55), X(0x7ff3afc4), X(0x7ff3d3e4),
  X(0x7ff3f7b4), X(0x7ff41b35), X(0x7ff43e69), X(0x7ff4614e),
  X(0x7ff483e6), X(0x7ff4a631), X(0x7ff4c82f), X(0x7ff4e9e1),
  X(0x7ff50b47), X(0x7ff52c62), X(0x7ff54d33), X(0x7ff56db9),
  X(0x7ff58df5), X(0x7ff5ade7), X(0x7ff5cd90), X(0x7ff5ecf1),
  X(0x7ff60c09), X(0x7ff62ada), X(0x7ff64963), X(0x7ff667a5),
  X(0x7ff685a1), X(0x7ff6a357), X(0x7ff6c0c7), X(0x7ff6ddf1),
  X(0x7ff6fad7), X(0x7ff71778), X(0x7ff733d6), X(0x7ff74fef),
  X(0x7ff76bc6), X(0x7ff78759), X(0x7ff7a2ab), X(0x7ff7bdba),
  X(0x7ff7d888), X(0x7ff7f315), X(0x7ff80d61), X(0x7ff8276c),
  X(0x7ff84138), X(0x7ff85ac4), X(0x7ff87412), X(0x7ff88d20),
  X(0x7ff8a5f0), X(0x7ff8be82), X(0x7ff8d6d7), X(0x7ff8eeef),
  X(0x7ff906c9), X(0x7ff91e68), X(0x7ff935cb), X(0x7ff94cf2),
  X(0x7ff963dd), X(0x7ff97a8f), X(0x7ff99105), X(0x7ff9a742),
  X(0x7ff9bd45), X(0x7ff9d30f), X(0x7ff9e8a0), X(0x7ff9fdf9),
  X(0x7ffa131a), X(0x7ffa2803), X(0x7ffa3cb4), X(0x7ffa512f),
  X(0x7ffa6573), X(0x7ffa7981), X(0x7ffa8d59), X(0x7ffaa0fc),
  X(0x7ffab46a), X(0x7ffac7a3), X(0x7ffadaa8), X(0x7ffaed78),
  X(0x7ffb0015), X(0x7ffb127f), X(0x7ffb24b6), X(0x7ffb36bb),
  X(0x7ffb488d), X(0x7ffb5a2e), X(0x7ffb6b9d), X(0x7ffb7cdb),
  X(0x7ffb8de9), X(0x7ffb9ec6), X(0x7ffbaf73), X(0x7ffbbff1),
  X(0x7ffbd03f), X(0x7ffbe05e), X(0x7ffbf04f), X(0x7ffc0012),
  X(0x7ffc0fa6), X(0x7ffc1f0d), X(0x7ffc2e47), X(0x7ffc3d54),
  X(0x7ffc4c35), X(0x7ffc5ae9), X(0x7ffc6971), X(0x7ffc77ce),
  X(0x7ffc8600), X(0x7ffc9407), X(0x7ffca1e4), X(0x7ffcaf96),
  X(0x7ffcbd1f), X(0x7ffcca7e), X(0x7ffcd7b4), X(0x7ffce4c1),
  X(0x7ffcf1a5), X(0x7ffcfe62), X(0x7ffd0af6), X(0x7ffd1763),
  X(0x7ffd23a9), X(0x7ffd2fc8), X(0x7ffd3bc1), X(0x7ffd4793),
  X(0x7ffd533f), X(0x7ffd5ec5), X(0x7ffd6a27), X(0x7ffd7563),
  X(0x7ffd807a), X(0x7ffd8b6e), X(0x7ffd963d), X(0x7ffda0e8),
  X(0x7ffdab70), X(0x7ffdb5d5), X(0x7ffdc017), X(0x7ffdca36),
  X(0x7ffdd434), X(0x7ffdde0f), X(0x7ffde7c9), X(0x7ffdf161),
  X(0x7ffdfad8), X(0x7ffe042f), X(0x7ffe0d65), X(0x7ffe167b),
  X(0x7ffe1f71), X(0x7ffe2848), X(0x7ffe30ff), X(0x7ffe3997),
  X(0x7ffe4211), X(0x7ffe4a6c), X(0x7ffe52a9), X(0x7ffe5ac8),
  X(0x7ffe62c9), X(0x7ffe6aae), X(0x7ffe7275), X(0x7ffe7a1f),
  X(0x7ffe81ad), X(0x7ffe891f), X(0x7ffe9075), X(0x7ffe97b0),
  X(0x7ffe9ece), X(0x7ffea5d2), X(0x7ffeacbb), X(0x7ffeb38a),
  X(0x7ffeba3e), X(0x7ffec0d8), X(0x7ffec758), X(0x7ffecdbf),
  X(0x7ffed40d), X(0x7ffeda41), X(0x7ffee05d), X(0x7ffee660),
  X(0x7ffeec4b), X(0x7ffef21f), X(0x7ffef7da), X(0x7ffefd7e),
  X(0x7fff030b), X(0x7fff0881), X(0x7fff0de0), X(0x7fff1328),
  X(0x7fff185b), X(0x7fff1d77), X(0x7fff227e), X(0x7fff276f),
  X(0x7fff2c4b), X(0x7fff3112), X(0x7fff35c4), X(0x7fff3a62),
  X(0x7fff3eeb), X(0x7fff4360), X(0x7fff47c2), X(0x7fff4c0f),
  X(0x7fff504a), X(0x7fff5471), X(0x7fff5885), X(0x7fff5c87),
  X(0x7fff6076), X(0x7fff6452), X(0x7fff681d), X(0x7fff6bd6),
  X(0x7fff6f7d), X(0x7fff7313), X(0x7fff7698), X(0x7fff7a0c),
  X(0x7fff7d6f), X(0x7fff80c2), X(0x7fff8404), X(0x7fff8736),
  X(0x7fff8a58), X(0x7fff8d6b), X(0x7fff906e), X(0x7fff9362),
  X(0x7fff9646), X(0x7fff991c), X(0x7fff9be3), X(0x7fff9e9c),
  X(0x7fffa146), X(0x7fffa3e2), X(0x7fffa671), X(0x7fffa8f1),
  X(0x7fffab65), X(0x7fffadca), X(0x7fffb023), X(0x7fffb26f),
  X(0x7fffb4ae), X(0x7fffb6e0), X(0x7fffb906), X(0x7fffbb20),
  X(0x7fffbd2e), X(0x7fffbf30), X(0x7fffc126), X(0x7fffc311),
  X(0x7fffc4f1), X(0x7fffc6c5), X(0x7fffc88f), X(0x7fffca4d),
  X(0x7fffcc01), X(0x7fffcdab), X(0x7fffcf4a), X(0x7fffd0e0),
  X(0x7fffd26b), X(0x7fffd3ec), X(0x7fffd564), X(0x7fffd6d2),
  X(0x7fffd838), X(0x7fffd993), X(0x7fffdae6), X(0x7fffdc31),
  X(0x7fffdd72), X(0x7fffdeab), X(0x7fffdfdb), X(0x7fffe104),
  X(0x7fffe224), X(0x7fffe33c), X(0x7fffe44d), X(0x7fffe556),
  X(0x7fffe657), X(0x7fffe751), X(0x7fffe844), X(0x7fffe930),
  X(0x7fffea15), X(0x7fffeaf3), X(0x7fffebca), X(0x7fffec9b),
  X(0x7fffed66), X(0x7fffee2a), X(0x7fffeee8), X(0x7fffefa0),
  X(0x7ffff053), X(0x7ffff0ff), X(0x7ffff1a6), X(0x7ffff247),
  X(0x7ffff2e4), X(0x7ffff37a), X(0x7ffff40c), X(0x7ffff499),
  X(0x7ffff520), X(0x7ffff5a3), X(0x7ffff621), X(0x7ffff69b),
  X(0x7ffff710), X(0x7ffff781), X(0x7ffff7ee), X(0x7ffff857),
  X(0x7ffff8bb), X(0x7ffff91c), X(0x7ffff979), X(0x7ffff9d2),
  X(0x7ffffa27), X(0x7ffffa79), X(0x7ffffac8), X(0x7ffffb13),
  X(0x7ffffb5b), X(0x7ffffba0), X(0x7ffffbe2), X(0x7ffffc21),
  X(0x7ffffc5d), X(0x7ffffc96), X(0x7ffffccd), X(0x7ffffd01),
  X(0x7ffffd32), X(0x7ffffd61), X(0x7ffffd8e), X(0x7ffffdb8),
  X(0x7ffffde0), X(0x7ffffe07), X(0x7ffffe2b), X(0x7ffffe4d),
  X(0x7ffffe6d), X(0x7ffffe8b), X(0x7ffffea8), X(0x7ffffec3),
  X(0x7ffffedc), X(0x7ffffef4), X(0x7fffff0a), X(0x7fffff1f),
  X(0x7fffff33), X(0x7fffff45), X(0x7fffff56), X(0x7fffff66),
  X(0x7fffff75), X(0x7fffff82), X(0x7fffff8f), X(0x7fffff9a),
  X(0x7fffffa5), X(0x7fffffaf), X(0x7fffffb8), X(0x7fffffc0),
  X(0x7fffffc8), X(0x7fffffce), X(0x7fffffd5), X(0x7fffffda),
  X(0x7fffffdf), X(0x7fffffe4), X(0x7fffffe8), X(0x7fffffeb),
  X(0x7fffffef), X(0x7ffffff1), X(0x7ffffff4), X(0x7ffffff6),
  X(0x7ffffff8), X(0x7ffffff9), X(0x7ffffffb), X(0x7ffffffc),
  X(0x7ffffffd), X(0x7ffffffd), X(0x7ffffffe), X(0x7fffffff),
  X(0x7fffffff), X(0x7fffffff), X(0x7fffffff), X(0x7fffffff),
  X(0x7fffffff), X(0x7fffffff), X(0x7fffffff), X(0x7fffffff),
  X(0x7fffffff), X(0x7fffffff), X(0x7fffffff), X(0x7fffffff),
};

#endif
