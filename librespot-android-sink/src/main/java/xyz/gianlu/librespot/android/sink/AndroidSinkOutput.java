package xyz.gianlu.librespot.android.sink;

import android.media.AudioFormat;
import android.media.AudioTrack;
import android.util.Log;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Range;
import org.jetbrains.annotations.VisibleForTesting;

import java.io.IOException;

import xyz.gianlu.librespot.player.mixing.output.OutputAudioFormat;
import xyz.gianlu.librespot.player.mixing.output.SinkException;
import xyz.gianlu.librespot.player.mixing.output.SinkOutput;

import static android.media.AudioFormat.CHANNEL_OUT_MONO;
import static android.media.AudioFormat.CHANNEL_OUT_STEREO;
import static android.media.AudioFormat.ENCODING_PCM_16BIT;

/**
 * <AUTHOR>
 */
public final class AndroidSinkOutput implements SinkOutput {
    private static final String TAG = "AndroidSinkOutput";
    private AudioTrack track;
    private float lastVolume = -1;

    @Override
    public boolean start(@NotNull OutputAudioFormat format) throws SinkException {
        Log.i(TAG, "Starting audio sink - Sample rate: " + format.getSampleRate() +
              "Hz, Channels: " + format.getChannels() + ", Bits: " + format.getSampleSizeInBits());

        if (format.getSampleSizeInBits() != 16) {
            Log.e(TAG, "Unsupported sample size: " + format.getSampleSizeInBits());
            throw new SinkException("Unsupported SampleSize", null);
        }
        if (format.getChannels() < 1 || format.getChannels() > 2) {
            Log.e(TAG, "Unsupported channel count: " + format.getChannels());
            throw new SinkException("Unsupported Number of Channels", null);
        }
        int pcmEncoding = ENCODING_PCM_16BIT;
        int channelConfig = format.getChannels() == 1 ? CHANNEL_OUT_MONO : CHANNEL_OUT_STEREO;
        int sampleRate = (int) format.getSampleRate();
        int minBufferSize = AudioTrack.getMinBufferSize(
                sampleRate,
                channelConfig,
                pcmEncoding
        );

        AudioFormat audioFormat = new AudioFormat.Builder()
                .setEncoding(pcmEncoding)
                .setSampleRate(sampleRate)
                .build();

        try {
            track = new AudioTrack.Builder()
                    .setBufferSizeInBytes(minBufferSize)
                    .setAudioFormat(audioFormat)
                    .setTransferMode(AudioTrack.MODE_STREAM)
                    .build();
            Log.i(TAG, "AudioTrack created successfully - Buffer size: " + minBufferSize + " bytes");
        } catch (UnsupportedOperationException e) {
            Log.e(TAG, "AudioTrack creation failed", e);
            throw new SinkException("AudioTrack creation failed in Sink: ", e.getCause());
        }

        if (lastVolume != -1) {
            track.setVolume(lastVolume);
            Log.d(TAG, "Restored volume: " + lastVolume);
        }

        track.play();
        Log.i(TAG, "AudioTrack started playing");
        return true;
    }

    @Override
    public void write(byte[] buffer, int offset, int len) throws IOException {
        int outcome = track.write(buffer, offset, len, AudioTrack.WRITE_BLOCKING);
        switch (outcome) {
            case AudioTrack.ERROR:
                throw new IOException("Generic Operation Failure while writing Track");
            case AudioTrack.ERROR_BAD_VALUE:
                throw new IOException("Invalid value used while writing Track");
            case AudioTrack.ERROR_DEAD_OBJECT:
                throw new IOException("Track Object has died in the meantime");
            case AudioTrack.ERROR_INVALID_OPERATION:
                throw new IOException("Failure due to improper use of Track Object methods");
        }
    }

    @Override
    public void flush() {
        if (track != null) track.flush();
    }

    @Override
    public boolean setVolume(@Range(from = 0L, to = 1L) float volume) {
        lastVolume = volume;
        if (track != null) track.setVolume(volume);
        return true;
    }

    @Override
    public void release() {
        if (track != null) track.release();
    }

    @Override
    public void stop() {
        if (track != null && track.getPlayState() != AudioTrack.PLAYSTATE_STOPPED) track.stop();
    }

    @Override
    public void close() {
        track = null;
    }

    @VisibleForTesting
    int getPlayState() {
        return track.getPlayState();
    }
}
